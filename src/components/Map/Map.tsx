/**
 * Map Component with marker and geofence navigation capabilities
 *
 * @example
 * ```tsx
 * // Create a ref to access navigation methods
 * const mapRef = useRef<MapRef>(null);
 *
 * // Navigate to specific marker
 * mapRef.current?.goToMarkerById('marker-1');
 *
 * // Navigate through markers
 * mapRef.current?.goToNextMarker();
 * mapRef.current?.goToPrevMarker();
 *
 * // Navigate through geofences
 * mapRef.current?.goToGeofenceById('geofence-1');
 * mapRef.current?.goToNextGeofence();
 * mapRef.current?.goToPrevGeofence();
 * ```
 */

import {Map<PERSON>ontainer, Marker, Polygon, Popup, TileLayer, Polyline} from "react-leaflet";
import {icon, LatLng, LatLngBounds, Map as LeafletMap} from "leaflet";
import "leaflet/dist/leaflet.css";
import {forwardRef, useImperativeHandle, useRef, useState, useEffect} from "react";
import {convertToLocalTimezone} from "../../utils/dateUtils";

// Highlighted versions of activity icons (red variants)
const highlightedActivityIcons = {
  sign_in_out: icon({
    iconUrl: "/map-marker/signinoff-red.png",
    shadowUrl: "/marker-shadow.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
  }),
  activity: icon({
    iconUrl: "/map-marker/activity-red.png",
    shadowUrl: "/marker-shadow.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
  }),
  alarm: icon({
    iconUrl: "/map-marker/alarm-red.png",
    shadowUrl: "/marker-shadow.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
  }),
  checkpoint: icon({
    iconUrl: "/map-marker/checkpoint-red.png",
    shadowUrl: "/marker-shadow.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
  }),
  form: icon({
    iconUrl: "/map-marker/form-red.png",
    shadowUrl: "/marker-shadow.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
  }),
  task: icon({
    iconUrl: "/map-marker/task-red.png",
    shadowUrl: "/marker-shadow.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
  }),
  geofence: icon({
    iconUrl: "/map-marker/geofence-red.png",
    shadowUrl: "/marker-shadow.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
  }),
};

// Activity-specific icons
const activityIcons = {
  sign_in_out: icon({
    iconUrl: "/map-marker/signinoff.png",
    shadowUrl: "/marker-shadow.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
  }),
  activity: icon({
    iconUrl: "/map-marker/activity.png",
    shadowUrl: "/marker-shadow.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
  }),
  alarm: icon({
    iconUrl: "/map-marker/alarm.png",
    shadowUrl: "/marker-shadow.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
  }),
  checkpoint: icon({
    iconUrl: "/map-marker/checkpoint.png",
    shadowUrl: "/marker-shadow.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
  }),
  form: icon({
    iconUrl: "/map-marker/form.png",
    shadowUrl: "/marker-shadow.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
  }),
  task: icon({
    iconUrl: "/map-marker/task.png",
    shadowUrl: "/marker-shadow.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
  }),
  geofence: icon({
    iconUrl: "/map-marker/geofence.png",
    shadowUrl: "/marker-shadow.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
  }),
};

interface MarkerData {
  id: string;
  position: [number, number]; // Explicitly type as tuple
  title: string;
  description: string;
  date: string; // ISO date string
  type?: 'sign_in_out' | 'activity' | 'alarm' | 'checkpoint' | 'form' | 'task' | 'geofence';
}

interface GeofenceData {
  id: string;
  geofence: [number, number][]; // Array of coordinate tuples
}

interface GeofenceActivity {
  uuid: string;
  geofence_name: string;
  zone_name: string;
  user_name: string;
  device_name: string;
  timezone_id: string;
  timezone_name: string;
  latitude: number | null;
  longitude: number | null;
  original_submitted_time: string;
}

interface MapProps {
  geofences: GeofenceData[];
  checkpointActivityMarkers: MarkerData[];
  geolocationActivityMarkers: MarkerData[];
  geofenceActivities?: GeofenceActivity[];
}

export interface MapRef {
  goToMarkerById: (id: string) => void;
  goToPrevMarker: () => void;
  goToNextMarker: () => void;
  goToGeofenceById: (id: string) => void;
  goToPrevGeofence: () => void;
  goToNextGeofence: () => void;
  goToGeolocationMarkerById: (id: string) => void;
  goToPrevGeolocationMarker: () => void;
  goToNextGeolocationMarker: () => void;
  fitAllBounds: () => void;
}

const Map = forwardRef<MapRef, MapProps>(({geofences, checkpointActivityMarkers, geolocationActivityMarkers = [], geofenceActivities = []}, ref) => {
  const mapRef = useRef<LeafletMap | null>(null);
  const [currentMarkerIndex, setCurrentMarkerIndex] = useState<number>(-1);
  const [currentGeofenceIndex, setCurrentGeofenceIndex] = useState<number>(-1);
  const [currentGeolocationMarkerIndex, setCurrentGeolocationMarkerIndex] = useState<number>(-1);
  const [isUserNavigating, setIsUserNavigating] = useState<boolean>(false);
  const [hasInitialFit, setHasInitialFit] = useState<boolean>(false);

  // Function to get icon based on marker type
  const getMarkerIcon = (marker: MarkerData, isHighlighted: boolean = false) => {
    // If marker has a type, use the appropriate icon
    if (marker.type) {
      if (isHighlighted && highlightedActivityIcons[marker.type]) {
        return highlightedActivityIcons[marker.type];
      }
      if (activityIcons[marker.type]) {
        return activityIcons[marker.type];
      }
    }
    
    // Fallback to checkpoint icon if no type is specified
    return isHighlighted ? highlightedActivityIcons.checkpoint : activityIcons.checkpoint;
  };

  // Function to group geofence activities by user and create polylines
  const createGeofencePolylines = () => {
    // Group activities by user_name
    const groupedByUser = geofenceActivities.reduce((acc, activity) => {
      if (activity.latitude && activity.longitude) {
        if (!acc[activity.user_name]) {
          acc[activity.user_name] = [];
        }
        acc[activity.user_name].push(activity);
      }
      return acc;
    }, {} as Record<string, GeofenceActivity[]>);

    // Sort each user's activities by time and create polylines
    const polylines = Object.entries(groupedByUser).map(([userName, activities], userIndex) => {
      // Sort by time
      const sortedActivities = activities.sort((a, b) => 
        new Date(a.original_submitted_time).getTime() - new Date(b.original_submitted_time).getTime()
      );

      // Create positions array
      const positions: [number, number][] = sortedActivities.map(activity => 
        [activity.latitude!, activity.longitude!]
      );

      // Generate different colors for different users
      const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
      const color = colors[userIndex % colors.length];

      return {
        key: `geofence-polyline-${userName}`,
        positions,
        color,
        userName,
        activities: sortedActivities
      };
    });

    return polylines;
  };

  const geofencePolylines = createGeofencePolylines();

  // Calculate bounds for auto-centering
  const calculateBounds = () => {
    const bounds = new LatLngBounds([]);
    let hasPoints = false;

    // Add checkpoint marker positions to bounds
    checkpointActivityMarkers.forEach(marker => {
      bounds.extend(new LatLng(marker.position[0], marker.position[1]));
      hasPoints = true;
    });

    // Add geolocation marker positions to bounds
    geolocationActivityMarkers.forEach(marker => {
      bounds.extend(new LatLng(marker.position[0], marker.position[1]));
      hasPoints = true;
    });

    // Add geofence coordinates to bounds
    geofences.forEach(({geofence}) => {
      geofence.forEach(coord => {
        bounds.extend(new LatLng(coord[0], coord[1]));
        hasPoints = true;
      });
    });

    // Add geofence activities to bounds (from polylines)
    geofenceActivities.forEach(activity => {
      if (activity.latitude && activity.longitude) {
        bounds.extend(new LatLng(activity.latitude, activity.longitude));
        hasPoints = true;
      }
    });

    return { bounds, hasPoints };
  };

  const { bounds, hasPoints } = calculateBounds();

  // Effect to fit bounds when data changes (only for initial load or when not user navigating)
  useEffect(() => {
    if (mapRef.current && hasPoints && bounds.isValid() && !isUserNavigating && !hasInitialFit) {
      // Small delay to ensure map is fully rendered
      setTimeout(() => {
        mapRef.current?.fitBounds(bounds, {
          padding: [20, 20],
          maxZoom: 16
        });
        setHasInitialFit(true);
      }, 100);
    }
  }, [checkpointActivityMarkers, geolocationActivityMarkers, geofences, geofenceActivities, hasPoints, bounds, isUserNavigating, hasInitialFit]);

  // Helper function to calculate center of geofence
  const calculateGeofenceCenter = (geofence: [number, number][]): [number, number] => {
    const lat = geofence.reduce((sum, coord) => sum + coord[0], 0) / geofence.length;
    const lng = geofence.reduce((sum, coord) => sum + coord[1], 0) / geofence.length;
    return [lat, lng];
  };

  // Navigation functions
  const goToPosition = (position: [number, number], zoom: number = 18) => {
    setIsUserNavigating(true);
    mapRef.current?.flyTo(position, zoom, {
      duration: 1.5, // Animation duration in seconds
    });
    // Reset user navigation flag after animation
    setTimeout(() => {
      setIsUserNavigating(false);
    }, 2000);
  };

  const goToMarkerById = (id: string) => {
    const marker = checkpointActivityMarkers.find(m => m.id === id);
    if (marker) {
      const index = checkpointActivityMarkers.indexOf(marker);
      setCurrentMarkerIndex(index);
      goToPosition(marker.position);
    }
  };

  const goToPrevMarker = () => {
    if (checkpointActivityMarkers.length === 0) return;
    const newIndex = currentMarkerIndex <= 0 
      ? checkpointActivityMarkers.length - 1 
      : currentMarkerIndex - 1;
    setCurrentMarkerIndex(newIndex);
    goToPosition(checkpointActivityMarkers[newIndex].position);
  };

  const goToNextMarker = () => {
    if (checkpointActivityMarkers.length === 0) return;
    const newIndex = currentMarkerIndex >= checkpointActivityMarkers.length - 1 
      ? 0 
      : currentMarkerIndex + 1;
    setCurrentMarkerIndex(newIndex);
    goToPosition(checkpointActivityMarkers[newIndex].position);
  };

  const goToGeofenceById = (id: string) => {
    const geofence = geofences.find(g => g.id === id);
    if (geofence) {
      const index = geofences.indexOf(geofence);
      setCurrentGeofenceIndex(index);
      goToPosition(calculateGeofenceCenter(geofence.geofence));
    }
  };

  const goToPrevGeofence = () => {
    if (geofences.length === 0) return;
    const newIndex = currentGeofenceIndex <= 0 
      ? geofences.length - 1 
      : currentGeofenceIndex - 1;
    setCurrentGeofenceIndex(newIndex);
    goToPosition(calculateGeofenceCenter(geofences[newIndex].geofence));
  };

  const goToNextGeofence = () => {
    if (geofences.length === 0) return;
    const newIndex = currentGeofenceIndex >= geofences.length - 1 
      ? 0 
      : currentGeofenceIndex + 1;
    setCurrentGeofenceIndex(newIndex);
    goToPosition(calculateGeofenceCenter(geofences[newIndex].geofence));
  };

  // Geolocation marker navigation functions
  const goToGeolocationMarkerById = (id: string) => {
    const marker = geolocationActivityMarkers.find(m => m.id === id);
    if (marker) {
      const index = geolocationActivityMarkers.indexOf(marker);
      setCurrentGeolocationMarkerIndex(index);
      goToPosition(marker.position);
    }
  };

  const goToPrevGeolocationMarker = () => {
    if (geolocationActivityMarkers.length === 0) return;
    const newIndex = currentGeolocationMarkerIndex <= 0 
      ? geolocationActivityMarkers.length - 1 
      : currentGeolocationMarkerIndex - 1;
    setCurrentGeolocationMarkerIndex(newIndex);
    goToPosition(geolocationActivityMarkers[newIndex].position);
  };

  const goToNextGeolocationMarker = () => {
    if (geolocationActivityMarkers.length === 0) return;
    const newIndex = currentGeolocationMarkerIndex >= geolocationActivityMarkers.length - 1 
      ? 0 
      : currentGeolocationMarkerIndex + 1;
    setCurrentGeolocationMarkerIndex(newIndex);
    goToPosition(geolocationActivityMarkers[newIndex].position);
  };

  const fitAllBounds = () => {
    if (mapRef.current && hasPoints && bounds.isValid()) {
      setIsUserNavigating(false);
      mapRef.current.fitBounds(bounds, {
        padding: [20, 20],
        maxZoom: 16
      });
    }
  };

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    goToMarkerById,
    goToPrevMarker,
    goToNextMarker,
    goToGeofenceById,
    goToPrevGeofence,
    goToNextGeofence,
    goToGeolocationMarkerById,
    goToPrevGeolocationMarker,
    goToNextGeolocationMarker,
    fitAllBounds,
  }));

  return (
    <MapContainer
      ref={mapRef}
      bounds={
        hasPoints && bounds.isValid() ? bounds : undefined
      }
      boundsOptions={{
        padding: [20, 20], // Add padding around bounds
        maxZoom: 16 // Prevent zooming too close
      }}
      center={
        !hasPoints || !bounds.isValid() ? [-6.2088, 106.8456] : undefined // Jakarta center as fallback
      }
      zoom={
        !hasPoints || !bounds.isValid() ? 10 : undefined // Default zoom for fallback
      }
      className="h-full w-full"
      zoomControl={true}
    >
      <TileLayer
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
      />

      {/* Render Geofences */}
      {geofences.map(({id, geofence}) => (
        <Polygon
          key={`geofence-${id}`}
          positions={geofence}
          pathOptions={{
            color: currentGeofenceIndex === geofences.findIndex(g => g.id === id) ? 'red' : 'blue',
            fillColor: currentGeofenceIndex === geofences.findIndex(g => g.id === id) ? 'red' : 'blue',
            fillOpacity: 0.2,
            weight: currentGeofenceIndex === geofences.findIndex(g => g.id === id) ? 3 : 2,
          }}
        />
      ))}

      {/* Render Geofence Activity Polylines */}
      {geofencePolylines.map((polyline) => (
        <Polyline
          key={polyline.key}
          positions={polyline.positions}
          pathOptions={{
            color: polyline.color,
            weight: 3,
            opacity: 0.8,
            dashArray: '5, 10'
          }}
        >
          <Popup>
            <div>
              <h3 className="font-bold">{polyline.userName}</h3>
              <p className="text-sm">Geofence Path</p>
              <p className="text-xs text-gray-500">
                {polyline.activities.length} points
              </p>
              <p className="text-xs text-gray-500">
                From: {convertToLocalTimezone(polyline.activities[0]?.original_submitted_time)?.format("DD MMM YYYY HH:mm")}
              </p>
              <p className="text-xs text-gray-500">
                To: {convertToLocalTimezone(polyline.activities[polyline.activities.length - 1]?.original_submitted_time)?.format("DD MMM YYYY HH:mm")}
              </p>
            </div>
          </Popup>
        </Polyline>
      ))}

      {/* Render Checkpoint Activity Markers */}
      {checkpointActivityMarkers.map((marker, index) => (
        <Marker
          key={`checkpoint-marker-${marker.id}`}
          position={marker.position}
          icon={getMarkerIcon(marker, index === currentMarkerIndex)}
        >
          <Popup>
            <div>
              <h3 className="font-bold">{marker.title}</h3>
              <p>{marker.description}</p>
              <p className="text-sm text-gray-500">
                {convertToLocalTimezone(marker.date)?.format("DD MMM YYYY HH:mm")}
              </p>
            </div>
          </Popup>
        </Marker>
      ))}

      {/* Render Geolocation Activity Markers */}
      {geolocationActivityMarkers.map((marker, index) => (
        <Marker
          key={`geolocation-marker-${marker.id}`}
          position={marker.position}
          icon={getMarkerIcon(marker, index === currentGeolocationMarkerIndex)}
        >
          <Popup>
            <div>
              <h3 className="font-bold">{marker.title}</h3>
              <p>{marker.description}</p>
              <p className="text-sm text-gray-500">
                {convertToLocalTimezone(marker.date)?.format("DD MMM YYYY HH:mm")}
              </p>
            </div>
          </Popup>
        </Marker>
      ))}
    </MapContainer>
  );
});

Map.displayName = "Map";

export default Map;
