import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import {
  AlertAdmin,
  EAlertAction,
  EAlertConditionType,
  EAlertEvent,
  EAlertLogicalConditionType,
  EAlertOperatorConditionType,
  AlertConditionRequest,
  AlertRecipientRequest,
  CreateAlertRequest,
} from "../../../services/mainApi/admin/types/alert.admin.mainApi.types";
import { v4 as uuidv4 } from "uuid";
import { Branch } from "../../../services/mainApi/sysadmin/types/branch.mainApi.types";
import { RootState } from "../..";
import alertAdminMainApi from "../../../services/mainApi/admin/alert.admin.mainApi";
import { isAxiosError } from "axios";
import { handleApiError } from "../../../utils/error";

export interface AddAlertAdminState {
  // Modal
  open: boolean;
  selectedAlert: AlertAdmin | null;
  mode: "create" | "update";
  loading: boolean;
  submitting: boolean;
  errorMessage: string[] | string | null;

  // Form fields
  name: string;
  description: string;
  event: EAlertEvent;
  action: EAlertAction;
  logicalConditionType: EAlertLogicalConditionType;
  subject: string;
  message: string;
  conditions: {
    rowUuid: string;
    type: EAlertConditionType;
    value: string | number | null;
    logicalOperatorConditionType: EAlertOperatorConditionType;
  }[];
  recipients: {
    rowUuid: string;
    type: "EMAIL" | "PHONE";
    value: string;
  }[];
  branchIds: string[];
  active: boolean;
}

const initialState: AddAlertAdminState = {
  // Modal
  open: false,
  mode: "create",
  loading: false,
  submitting: false,
  errorMessage: null,
  selectedAlert: null,

  // Form fields
  name: "",
  description: "",
  event: EAlertEvent.ACTIVITY_SUBMITTED,
  action: EAlertAction.EMAIL,
  logicalConditionType: EAlertLogicalConditionType.AND,
  subject: "",
  message: "",
  conditions: [],
  recipients: [],
  branchIds: [],
  active: true,
};

export const createAlertAdmin = createAsyncThunk(
  "addAlertAdmin/create",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const addAlertState = state.addAlertAdmin;

      // Prepare conditions for API request
      const conditions: AlertConditionRequest[] =
        addAlertState.conditions.map((condition) => ({
          alert_condition_type_id: Number(condition.type),
          alert_operator_condition_type:
            condition.logicalOperatorConditionType,
          alert_condition_value_id: Number(
            condition.value || 0
          ),
          alert_condition_description: "", // Add description if needed
          active: true,
        }));

      // Prepare recipients for API request
      const recipients: AlertRecipientRequest[] =
        addAlertState.recipients.map((recipient) => ({
          recipient_type: recipient.type,
          recipient_contact: recipient.value,
        }));

      // Prepare payload for API request
      const payload: CreateAlertRequest = {
        alert_name: addAlertState.name,
        alert_description: addAlertState.description || "",
        alert_action_id: Number(addAlertState.action),
        alert_event_id: Number(addAlertState.event),
        alert_logical_condition_type:
          addAlertState.logicalConditionType,
        subject: addAlertState.subject,
        message: addAlertState.message,
        conditions: conditions,
        recipients: recipients,
        branch_ids: addAlertState.branchIds.map((id) =>
          Number(id)
        ),
        active: addAlertState.active,
      };

      const response = await alertAdminMainApi.createAlert(
        payload,
        branchCode
      );
      return response.data;
    } catch (e) {
      if (isAxiosError(e)) {
        const { errors } = handleApiError(e);
        return rejectWithValue(errors);
      }
      return rejectWithValue(["Failed to create alert"]);
    }
  }
);

export const updateAlertAdmin = createAsyncThunk(
  "addAlertAdmin/update",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const addAlertState = state.addAlertAdmin;

      if (!addAlertState.selectedAlert?.id) {
        return rejectWithValue([
          "No alert selected for update",
        ]);
      }

      // Prepare conditions for API request
      const conditions: AlertConditionRequest[] =
        addAlertState.conditions.map((condition) => ({
          alert_condition_type_id: Number(condition.type),
          alert_operator_condition_type:
            condition.logicalOperatorConditionType,
          alert_condition_value_id: Number(
            condition.value || 0
          ),
          alert_condition_description: "", // Add description if needed
          active: true,
        }));

      // Prepare recipients for API request
      const recipients: AlertRecipientRequest[] =
        addAlertState.recipients.map((recipient) => ({
          recipient_type: recipient.type,
          recipient_contact: recipient.value,
        }));

      // Prepare payload for API request
      const payload: CreateAlertRequest = {
        alert_name: addAlertState.name,
        alert_description: addAlertState.description || "",
        alert_action_id: Number(addAlertState.action),
        alert_event_id: Number(addAlertState.event),
        alert_logical_condition_type:
          addAlertState.logicalConditionType,
        subject: addAlertState.subject,
        message: addAlertState.message,
        conditions: conditions,
        recipients: recipients,
        branch_ids: addAlertState.branchIds.map((id) =>
          Number(id)
        ),
        active: addAlertState.active,
      };

      const response = await alertAdminMainApi.updateAlert(
        payload,
        branchCode,
        addAlertState.selectedAlert.id
      );
      return response.data;
    } catch (e) {
      if (isAxiosError(e)) {
        const { errors } = handleApiError(e);
        return rejectWithValue(errors);
      }
      return rejectWithValue(["Failed to update alert"]);
    }
  }
);

const addAlertAdminSlice = createSlice({
  name: "addAlertAdmin",
  initialState,
  reducers: {
    // Modal
    setOpen: (state, action) => {
      state.open = action.payload;
      state.mode = "create";
    },
    openUpdate: (
      state,
      action: PayloadAction<AlertAdmin>
    ) => {
      state.open = true;
      state.mode = "update";
      state.selectedAlert = action.payload;
      state.name = action.payload.alert_name;
      state.description = action.payload.alert_description;
      state.event = action.payload.alert_event
        .id as EAlertEvent;
      state.action = action.payload.alert_action
        .id as EAlertAction;
      state.logicalConditionType = action.payload
        .alert_logical_condition_type as EAlertLogicalConditionType;
      state.subject = action.payload.subject;
      state.message = action.payload.message;
      state.conditions = action.payload.conditions.map(
        (condition) => ({
          rowUuid: condition.id,
          type: condition.alert_condition_type_id,
          value: condition.alert_condition_value_id,
          logicalOperatorConditionType:
            condition.alert_operator_condition_type,
        })
      );
      state.recipients = action.payload.recipients.map(
        (recipient) => ({
          rowUuid: recipient.id,
          type: recipient.recipient_type as
            | "EMAIL"
            | "PHONE",
          value: recipient.recipient_contact || "",
        })
      );
      state.branchIds = action.payload.branches.map(
        (branch) => branch.id
      );
      state.active = action.payload.active;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setSubmitting: (state, action) => {
      state.submitting = action.payload;
    },
    close: () => {
      return {
        ...initialState,
      };
    },

    // Form fields
    setName: (state, action: PayloadAction<string>) => {
      state.name = action.payload;
    },
    setDescription: (
      state,
      action: PayloadAction<string>
    ) => {
      state.description = action.payload;
    },
    setSubject: (state, action: PayloadAction<string>) => {
      state.subject = action.payload;
    },
    setMessage: (state, action: PayloadAction<string>) => {
      state.message = action.payload;
    },
    setLogicalConditionType: (
      state,
      action: PayloadAction<EAlertLogicalConditionType>
    ) => {
      state.logicalConditionType = action.payload;
    },
    setEvent: (
      state,
      action: PayloadAction<EAlertEvent>
    ) => {
      state.event = action.payload;
    },
    setAction: (
      state,
      action: PayloadAction<EAlertAction>
    ) => {
      state.action = action.payload;
    },

    // Conditions
    addCondition: (state) => {
      state.conditions.push({
        rowUuid: uuidv4(),
        type: EAlertConditionType.USER,
        value: "",
        logicalOperatorConditionType:
          EAlertOperatorConditionType.EQUAL,
      });
    },
    removeCondition: (
      state,
      action: PayloadAction<string>
    ) => {
      state.conditions = state.conditions.filter(
        (condition) => condition.rowUuid !== action.payload
      );
    },
    changeType: (
      state,
      action: PayloadAction<{
        rowUuid: string;
        type: EAlertConditionType;
      }>
    ) => {
      const { rowUuid, type } = action.payload;
      const index = state.conditions.findIndex(
        (condition) => condition.rowUuid === rowUuid
      );
      if (index !== -1) {
        state.conditions[index].type = type;
        state.conditions[index].value = "";
      }
    },
    changeOperatorCondition: (
      state,
      action: PayloadAction<{
        rowUuid: string;
        logicalOperatorConditionType: EAlertOperatorConditionType;
      }>
    ) => {
      const { rowUuid, logicalOperatorConditionType } =
        action.payload;
      const index = state.conditions.findIndex(
        (condition) => condition.rowUuid === rowUuid
      );
      if (index !== -1) {
        state.conditions[
          index
        ].logicalOperatorConditionType =
          logicalOperatorConditionType;
      }
    },
    changeValue: (
      state,
      action: PayloadAction<{
        rowUuid: string;
        value: string | number | null;
      }>
    ) => {
      const { rowUuid, value } = action.payload;
      const index = state.conditions.findIndex(
        (condition) => condition.rowUuid === rowUuid
      );
      if (index !== -1) {
        state.conditions[index].value = value;
      }
    },

    // Recipients
    addRecipient: (state) => {
      state.recipients.push({
        rowUuid: uuidv4(),
        type: "EMAIL",
        value: "",
      });
    },
    removeRecipient: (
      state,
      action: PayloadAction<string>
    ) => {
      state.recipients = state.recipients.filter(
        (recipient) => recipient.rowUuid !== action.payload
      );
    },
    changeRecipient: (
      state,
      action: PayloadAction<{
        rowUuid: string;
        value: string;
      }>
    ) => {
      const { rowUuid, value } = action.payload;
      const index = state.recipients.findIndex(
        (recipient) => recipient.rowUuid === rowUuid
      );
      if (index !== -1) {
        state.recipients[index].value = value;
      }
    },

    setBranchIds: (
      state,
      action: PayloadAction<Branch[]>
    ) => {
      state.branchIds = action.payload.map(
        (branch) => branch.id
      );
    },
    setActive: (state, action: PayloadAction<boolean>) => {
      state.active = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Create alert cases
      .addCase(createAlertAdmin.pending, (state) => {
        state.submitting = true;
        state.errorMessage = null;
      })
      .addCase(createAlertAdmin.fulfilled, () => {
        return initialState;
      })
      .addCase(
        createAlertAdmin.rejected,
        (state, action) => {
          state.submitting = false;
          state.errorMessage = action.payload as
            | string
            | string[];
        }
      )

      // Update alert cases
      .addCase(updateAlertAdmin.pending, (state) => {
        state.submitting = true;
        state.errorMessage = null;
      })
      .addCase(updateAlertAdmin.fulfilled, () => {
        return initialState;
      })
      .addCase(
        updateAlertAdmin.rejected,
        (state, action) => {
          console.log(action.payload);
          state.submitting = false;
          state.errorMessage = action.payload as
            | string
            | string[];
        }
      );
  },
});

export default addAlertAdminSlice;
