import {
  createSlice,
  createAsyncThunk,
} from "@reduxjs/toolkit";
import { AlertAdmin } from "../../../services/mainApi/admin/types/alert.admin.mainApi.types";
import alertAdminMainApi from "../../../services/mainApi/admin/alert.admin.mainApi";
import { RootState } from "../..";
import {
  BaseGetRequest,
  BasePaginationMeta,
} from "../../../services/mainApi/types/base.mainApi.types";

// Extend BaseGetRequest to include alert_type
interface AlertGetRequest extends BaseGetRequest {
  alert_type?: string;
}

interface AlertAdminState {
  alerts: AlertAdmin[];
  loading: boolean;
  error: string | null;
  filter: {
    search: string | null;
    page: number | null;
    limit: number | null;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
    alertType: string | null;
  };
  pagination: BasePaginationMeta;
}

const initialState: AlertAdminState = {
  alerts: [],
  loading: false,
  error: null,
  filter: {
    search: null,
    page: 1,
    limit: 10,
    orderBy: "created_at",
    orderDirection: "DESC",
    alertType: null,
  },
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
    total_pages: 0,
  },
};

export const fetchAlertAdmin = createAsyncThunk<
  { data: AlertAdmin[]; meta: BasePaginationMeta },
  string,
  { state: RootState; rejectValue: string }
>(
  "alertAdmin/fetchAlerts",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState();
      const { filter } = state.alertAdmin || {
        filter: initialState.filter,
      };

      const params: AlertGetRequest = {
        search: filter.search || undefined,
        page: filter.page || undefined,
        limit: filter.limit || undefined,
        order_by: filter.orderBy,
        order_direction: filter.orderDirection,
        alert_type: filter.alertType || undefined,
        ignore_active_status: true,
      };

      const response = await alertAdminMainApi.getAlerts(
        params,
        branchCode
      );
      return {
        data: response.data || [],
        meta: response.meta,
      };
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue("Failed to fetch alerts");
    }
  }
);

const alertAdminSlice = createSlice({
  name: "alertAdmin",
  initialState,
  reducers: {
    setFilter: (state, action) => {
      state.filter = { ...state.filter, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchAlertAdmin.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        fetchAlertAdmin.fulfilled,
        (state, action) => {
          state.loading = false;
          state.alerts = action.payload.data;
          state.pagination = action.payload.meta;
        }
      )
      .addCase(
        fetchAlertAdmin.rejected,
        (state, action) => {
          state.loading = false;
          state.error = action.payload as string;
        }
      );
  },
});

export const { setFilter } = alertAdminSlice.actions;
export default alertAdminSlice;
