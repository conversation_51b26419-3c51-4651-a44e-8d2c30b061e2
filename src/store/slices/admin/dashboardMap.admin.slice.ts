import {createAsyncThunk, createSlice} from "@reduxjs/toolkit";
import {AdminRole} from "../../../services/mainApi/admin/types/roles.admin.mainApi.types.ts";
import {AdminUser} from "../../../services/mainApi/admin/types/user.admin.mainApi.types.ts";
import {RootState} from "../../index.ts";
import {dashboardAdminApi} from "../../../services/mainApi/admin/dashboard.admin.mainApi.ts";
import {isAxiosError} from "axios";
import {handleApiError} from "../../../utils/error.ts";
import {AdminGeofence} from "../../../services/mainApi/admin/types/geofence.admin.mainApi.types.ts";
import geofenceAdminMainApi from "../../../services/mainApi/admin/geofence.admin.mainApi.ts";
import {AdminDashboardData, AdminDashboardMeta} from "../../../services/mainApi/admin/types/dashboard.admin.mainApi.types.ts";
import dayjs from "dayjs";

interface State {
  fetchingDashboardData: boolean;
  fetchingGeofences: boolean;
  error: string[] | string | null;
  filters: {
    startDate: string;
    endDate: string;
    startTime: string;
    endTime: string;
    role: AdminRole | null;
    user: AdminUser | null;
    signonoff: boolean;
    activity: boolean;
    alarm: boolean;
    checkpoint: boolean;
    form: boolean;
    task: boolean;
    geofence: boolean;
  }

  dashboardData: AdminDashboardData | null;
  dashboardMeta: AdminDashboardMeta | null;
  geofences: AdminGeofence[];

  filterModal: {
    visible: boolean;
  }
}

const initialState: State = {
  fetchingDashboardData: false,
  fetchingGeofences: false,
  error: null,
  filters: {
    startDate: dayjs().subtract(3, 'day').format('YYYY-MM-DD'),
    endDate: dayjs().format('YYYY-MM-DD'),
    startTime: "00:00:00",
    endTime: "23:59:59",
    role: null,
    user: null,
    signonoff: true,
    activity: true,
    alarm: true,
    checkpoint: true,
    form: true,
    task: true,
    geofence: true,
  },
  filterModal: {
    visible: false,
  },
  dashboardData: null,
  dashboardMeta: null,
  geofences: [],
};

export const fetchDashboardData = createAsyncThunk(
  "dashboardMapAdmin/fetchDashboardData",
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as RootState;
      const branchCode = state.auth.user?.current_branch.branch_code;

      if (!branchCode) {
        return rejectWithValue("Branch code not found");
      }

      const { filters } = state.dashboardMapAdmin;

      const params: any = {
        start_date: filters.startDate || undefined,
        end_date: filters.endDate || undefined,
        start_time: filters.startTime || undefined,
        end_time: filters.endTime || undefined,
        role_id: filters.role?.id || undefined,
        user_id: filters.user?.id || undefined,
      };

      // Only add display options if they are true
      if (filters.signonoff) params.signonoff = true;
      if (filters.activity) params.activity = true;
      if (filters.alarm) params.alarm = true;
      if (filters.checkpoint) params.checkpoint = true;
      if (filters.form) params.form = true;
      if (filters.task) params.task = true;
      if (filters.geofence) params.geofence = true;

      const response = await dashboardAdminApi.getDashboardData(branchCode, params);

      if (!response.data) {
        return rejectWithValue(response.error);
      }

      return response;
    }
    catch (error) {
      if(isAxiosError(error)) {
        const apiError = handleApiError(error);
        return rejectWithValue(apiError.errors);
      }
      return rejectWithValue("Failed to fetch dashboard data");
    }
  }
);

export const fetchGeofencesDashboard = createAsyncThunk(
  "dashboardMapAdmin/fetchGeofencesDashboard",
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as RootState;
      const branchCode = state.auth.user?.current_branch.branch_code;

      if (!branchCode) {
        return rejectWithValue("Branch code not found");
      }

        const response = await geofenceAdminMainApi.getGeofences(branchCode);

      if (!response.data) {
        return rejectWithValue(response.error);
      }

      return response.data;
    }
    catch (error) {
      if(isAxiosError(error)) {
        const apiError = handleApiError(error);
        return rejectWithValue(apiError.errors);
      }
      return rejectWithValue("Failed to fetch geofences");
    }
  }
);



const dashboardMapAdminSlice = createSlice({
  name: "dashboardMapAdmin",
  initialState: {
    ...initialState,
  },
  reducers: {
    setLoading: (state, action) => {
      state.fetchingDashboardData = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    setDashboardData: (state, action) => {
      state.dashboardData = action.payload;
    },
    setFilter: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setStartDate: (state, action) => {
      state.filters.startDate = action.payload;
    },
    setEndDate: (state, action) => {
      state.filters.endDate = action.payload;
    },
    setStartTime: (state, action) => {
      state.filters.startTime = action.payload;
    },
    setEndTime: (state, action) => {
      state.filters.endTime = action.payload;
    },
    setRole: (state, action) => {
      state.filters.role = action.payload;
    },
    setUser: (state, action) => {
      state.filters.user = action.payload;
    },
    setDisplayOptions: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    toggleFilterModal: (state) => {
      state.filterModal.visible = !state.filterModal.visible;
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchDashboardData.pending, (state) => {
        state.fetchingDashboardData = true;
        state.error = null;
      })
      .addCase(fetchDashboardData.fulfilled, (state, action) => {
        state.fetchingDashboardData = false;
        state.dashboardData = action.payload.data;
        state.dashboardMeta = action.payload.meta;
      })
      .addCase(fetchDashboardData.rejected, (state, action) => {
        state.fetchingDashboardData = false;
        state.error = action.payload as string;
      });

    builder
      .addCase(fetchGeofencesDashboard.pending, (state) => {
        state.fetchingGeofences = true;
        state.error = null;
      })
      .addCase(fetchGeofencesDashboard.fulfilled, (state, action) => {
        state.fetchingGeofences = false;
        state.geofences = action.payload;
      })
      .addCase(fetchGeofencesDashboard.rejected, (state, action) => {
        state.fetchingGeofences = false;
        state.error = action.payload as string;
      });


  },
});

export default dashboardMapAdminSlice;
