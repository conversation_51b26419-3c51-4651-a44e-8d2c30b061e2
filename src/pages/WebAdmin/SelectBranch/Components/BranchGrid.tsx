import { Card, Empty, Spin } from "antd";
import { MdBusiness, MdVpnKey, MdAccessTime } from "react-icons/md";
import { Branch } from "../../../../services/mainApi/sysadmin/types/branch.mainApi.types.ts";
import { useTranslation } from "react-i18next";

interface BranchGridProps {
  branches: Branch[];
  onSelect: (branchCode: string) => void;
  loading?: boolean;
}

const BranchGrid = ({
  branches,
  onSelect,
  loading = false,
}: BranchGridProps) => {
  const { t } = useTranslation();

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[300px]">
        <Spin size="large" />
      </div>
    );
  }

  if (!branches.length) {
    return (
      <div className="flex justify-center items-center min-h-[300px]">
        <Empty
          description={
            <span className="text-gray-500 dark:text-gray-400">
              {t("selectBranch.noBranchesFound", "No branches found")}
            </span>
          }
        />
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {branches.map((branch) => (
        <Card
          key={branch.id}
          className="hover:shadow-lg hover:scale-[1.02] hover:border-blue-200 dark:hover:border-blue-700 transition-all duration-200 cursor-pointer group dark:bg-gray-800 dark:border-gray-700"
          onClick={() => onSelect(branch.branch_code)}
        >
          <div className="space-y-2">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center gap-2">
                  <MdBusiness className="text-blue-600 dark:text-blue-500" />
                  {branch.branch_name}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 flex items-center gap-1">
                  <MdVpnKey className="text-gray-400 dark:text-gray-500" />
                  {branch.license.license_name} License
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400 flex items-center gap-1">
                  <MdAccessTime className="text-gray-400 dark:text-gray-500" />
                  {branch.timezone.timezone_name} ({branch.timezone.gmt_offset})
                </p>
              </div>
              <div className="flex items-center gap-1">
                {branch.active && (
                  <div className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs px-2 py-1 rounded-full">
                    {t("selectBranch.status.active", "Active")}
                  </div>
                )}
                {!branch.active && (
                  <div className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs px-2 py-1 rounded-full">
                    {t("selectBranch.status.inactive", "Inactive")}
                  </div>
                )}
                {branch.id === branch.parent_id && (
                  <div className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-2 py-1 rounded-full">
                    {t("selectBranch.status.mainBranch", "Main Branch")}
                  </div>
                )}
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

export default BranchGrid;
