import React, {useState} from "react";
import {But<PERSON>, Dropdown, Spin, Tag, Tooltip} from "antd";
import {
  FiCheckSquare,
  FiClock,
  FiDownload,
  FiFileText,
  FiGrid,
  FiHash,
  FiInfo,
  FiSmartphone,
  FiTarget,
  FiUser,
} from "react-icons/fi";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import {useTranslation} from "react-i18next";
import {AdminCheckpointLogEntry} from "../../../../../services/mainApi/admin/types/checkpointLog.admin.mainApi.types";
import {checkpointLogAdminApi} from "../../../../../services/mainApi/admin/checkpointLog.admin.mainApi.ts";
import {useParams} from "react-router-dom";
import {formatDateTime, formatTimeAgo} from "../../../../../utils/dateUtils.ts";

dayjs.extend(relativeTime);

interface CheckpointActivityLogItemProps {
  activityLog: AdminCheckpointLogEntry;
}

const CheckpointActivityLogItem: React.FC<
  CheckpointActivityLogItemProps
> = ({activityLog}) => {
  const {t} = useTranslation();
  const [isDownloading, setIsDownloading] = useState(false);
  const {branchCode} = useParams<{
    branchCode: string;
  }>();

  const handleDownloadSpreadsheet = async (
    activityLog: AdminCheckpointLogEntry
  ) => {
    try {
      setIsDownloading(true);

      if (!branchCode) {
        console.error("Branch code is missing");
        return;
      }

      const blob =
        await checkpointLogAdminApi.exportCheckpointLogById(
          branchCode,
          activityLog.id,
          "spreadsheet",
          "buffer"
        );

      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `checkpoint_log_${activityLog.uuid}.xlsx`;
      link.click();

      URL.revokeObjectURL(url); // Cleanup
    } catch (error) {
      console.error(
        "Error downloading spreadsheet:",
        error
      );
    } finally {
      setIsDownloading(false);
    }
  };

  const downloadPdf = async () => {
    try {
      setIsDownloading(true);

      if (!branchCode) {
        console.error("Branch code is missing");
        return;
      }

      const blob =
        await checkpointLogAdminApi.exportCheckpointLogById(
          branchCode,
          activityLog.id,
          "pdf",
          "buffer"
        );

      // Create URL and open in new tab instead of downloading
      const url = URL.createObjectURL(blob);
      window.open(url, "_blank");

      // Still need to revoke the URL when done, but we need to delay it
      // to ensure the new tab has time to load the resource
      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 5000); // 5 seconds delay before cleanup
    } catch (error) {
      console.error("Error opening PDF:", error);
    } finally {
      setIsDownloading(false);
    }
  };

  const exportMenu = {
    items: [
      {
        key: "spreadsheet",
        icon: <FiGrid className="text-lg"/>,
        label: t(
          "checkpointActivityLog.downloadSpreadsheet",
          "Export as Spreadsheet"
        ),
        onClick: () =>
          handleDownloadSpreadsheet(activityLog),
      },
      {
        key: "pdf",
        icon: <FiFileText className="text-lg"/>,
        label: t(
          "checkpointActivityLog.downloadPdf",
          "Export as PDF"
        ),
        onClick: downloadPdf,
      },
    ],
  };

  return (
    <div
      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border-0 p-4 mb-4">
      {/* Header - with gradient background */}
      <div
        className="mb-3 -mx-4 -mt-4 px-4 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-800 rounded-t-xl border-b border-gray-100 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white flex items-center gap-2">
              {activityLog.checkpoint_name}
            </h3>
            <div className="flex gap-2 items-center">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {activityLog.uuid}
              </p>
              <Tag
                color={
                  activityLog.is_beacon ? "green" : "blue"
                }
              >
                {activityLog.is_beacon
                  ? "Beacon"
                  : "Regular"}
              </Tag>
            </div>
          </div>
          <Tooltip
            title={formatDateTime(
              activityLog.original_submitted_time,
              "DD MMM YYYY HH:mm",
              activityLog.timezone_name
            )}
          >
            <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
              <FiClock className="inline"/>
              {formatTimeAgo(
                activityLog.original_submitted_time,
                activityLog.timezone_name
              )}
            </span>
          </Tooltip>
        </div>
      </div>

      {/* Rearranged Layout */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* First Column */}
        <div className="space-y-3">
          {/* User Info */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-blue-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex-shrink-0">
              <FiUser className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("checkpointActivityLog.user", "User")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {activityLog.user_name}
                </span>
                {activityLog.role_name && (
                  <Tag color="blue">
                    {activityLog.role_name}
                  </Tag>
                )}
              </div>
            </div>
          </div>

          {/* Device Info */}
          {activityLog.device_name && (
            <div
              className="group flex items-center gap-2 text-sm hover:bg-purple-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
              <div
                className="flex items-center justify-center w-7 h-7 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 flex-shrink-0">
                <FiSmartphone className="w-4 h-4"/>
              </div>
              <div className="flex flex-col min-w-0">
                <div className="flex items-center gap-1">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {t(
                      "checkpointActivityLog.device",
                      "Device"
                    )}
                    :
                  </span>
                  <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                    {activityLog.device_name}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Timezone Info */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-teal-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-teal-100 dark:bg-teal-900 text-teal-600 dark:text-teal-300 flex-shrink-0">
              <FiClock className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t(
                    "checkpointActivityLog.timezone",
                    "Timezone"
                  )}
                  :
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {activityLog.timezone_name}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Second Column */}
        <div className="space-y-3">
          {/* Branch Info (Previously Checkpoint Info) */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-green-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 flex-shrink-0">
              <FiCheckSquare className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t(
                    "checkpointActivityLog.branchName",
                    "Branch"
                  )}
                  :
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {activityLog.branch_name}
                </span>
              </div>
            </div>
          </div>

          {/* Zone Info */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-indigo-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-300 flex-shrink-0">
              <FiInfo className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t(
                    "checkpointActivityLog.zoneName",
                    "Zone"
                  )}
                  :
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {activityLog.zone_name}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Third Column */}
        <div className="space-y-3">
          {/* Scan Target Info */}
          {activityLog.target_scan_count !== null && (
            <div
              className="group flex items-center gap-2 text-sm hover:bg-orange-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
              <div
                className="flex items-center justify-center w-7 h-7 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 flex-shrink-0">
                <FiTarget className="w-4 h-4"/>
              </div>
              <div className="flex flex-col min-w-0">
                <div className="flex items-center gap-1">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {t(
                      "checkpointActivityLog.targetScan",
                      "Target Scan"
                    )}
                    :
                  </span>
                  <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                    {activityLog.target_scan_count}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Beacon Info */}
          {activityLog.is_beacon && (
            <div
              className="group flex items-center gap-2 text-sm hover:bg-blue-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
              <div
                className="flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex-shrink-0">
                <FiHash className="w-4 h-4"/>
              </div>
              <div className="flex flex-col min-w-0">
                <div className="flex items-center gap-1">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {t(
                      "checkpointActivityLog.beaconValues",
                      "Beacon Values"
                    )}
                    :
                  </span>
                  <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                    Major: {activityLog.major_value}, Minor:{" "}
                    {activityLog.minor_value}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Footer Section with modern styling */}
      <div
        className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center bg-gray-50 dark:bg-gray-800 -mx-4 -mb-4 px-4 py-3 rounded-b-xl">
        <div className="text-xs text-gray-500 dark:text-gray-400 flex flex-wrap gap-2">
          <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md">
            {t("checkpointActivityLog.serialNumber", "S/N")}
            : {activityLog.serial_number}
          </span>
          <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md">
            {t(
              "checkpointActivityLog.submittedTime",
              "Submitted"
            )}
            :{" "}
            {
              formatDateTime(
                activityLog.original_submitted_time,
                "DD MMM YYYY HH:mm",
                activityLog.timezone_name
              )
            }
          </span>
        </div>
        <div className="flex gap-2">
          <Dropdown
            menu={exportMenu}
            placement="bottomRight"
          >
            <Button
              icon={
                isDownloading ? (
                  <Spin size="small"/>
                ) : (
                  <FiDownload/>
                )
              }
              disabled={isDownloading}
              className="flex items-center gap-2 transition-all duration-300"
            >
              {t("common.export", "Export")}
            </Button>
          </Dropdown>
        </div>
      </div>
    </div>
  );
};

export default CheckpointActivityLogItem;
