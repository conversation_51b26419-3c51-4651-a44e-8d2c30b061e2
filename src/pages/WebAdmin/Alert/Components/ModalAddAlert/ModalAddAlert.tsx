import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Divider, Form as AntForm, Input, Modal, Select, Switch,} from "antd";
import {FilterOutlined, MailOutlined, PlusOutlined,} from "@ant-design/icons";
import {useTranslation} from "react-i18next";
import {useAppDispatch, useAppSelector,} from "../../../../../store/hooks.ts";
import addAlertAdminSlice, {
  createAlertAdmin,
  updateAlertAdmin,
} from "../../../../../store/slices/admin/addAlert.admin.slice.ts";
import AlertConditionRow from "../AlertConditionRow/AlertConditionRow.tsx";
import AlertRecipientRow from "../AlertRecipientRow/AlertRecipientRow.tsx";
import {useParams} from "react-router-dom";
import {useCallback} from "react";
import {
  EAlertAction,
  EAlertEvent,
  EAlertLogicalConditionType,
} from "../../../../../services/mainApi/admin/types/alert.admin.mainApi.types.ts";
import GridSelectBranch from "../../../../../components/Branch/GridSelectBranch.tsx";
import ErrorMessage from "../../../../../components/Common/ErrorMessage/ErrorMessage.tsx";
import {fetchAlertAdmin} from "../../../../../store/slices/admin/alert.admin.slice.ts";

const {Option} = Select;

const ModalAddAlert = () => {
  const {t} = useTranslation();
  const dispatch = useAppDispatch();
  const {message} = App.useApp();
  const {branchCode} = useParams<{
    branchCode: string;
  }>();
  const addAlertState = useAppSelector(
    (state) => state.addAlertAdmin
  );
  const {actions} = addAlertAdminSlice;

  const onClose = () => {
    dispatch(actions.close());
  };

  const onSubmit = useCallback(async () => {
    if (!branchCode) {
      message.error(
        t(
          "alert.error.noBranchCode",
          "Branch code is required"
        )
      );
      return;
    }

    // Check if all recipients have values
    const emptyRecipients = addAlertState.recipients.filter(
      (r) => !r.value
    );
    if (emptyRecipients.length > 0) {
      message.error(
        t(
          "alert.error.emptyRecipient",
          "All recipients must have contact information"
        )
      );
      return;
    }

    try {
      if (addAlertState.mode === "create") {
        await dispatch(
          createAlertAdmin(branchCode)
        ).unwrap();
        message.success(
          t(
            "alert.modal.add.success",
            "Alert created successfully"
          )
        );
      } else {
        await dispatch(
          updateAlertAdmin(branchCode)
        ).unwrap();
        message.success(
          t(
            "alert.modal.update.success",
            "Alert updated successfully"
          )
        );
      }
      dispatch(fetchAlertAdmin(branchCode));
      onClose();
    } catch (error) {
      console.log("Error submitting alert:", error);
      const errorMessage =
        typeof error === "string"
          ? error
          : Array.isArray(error)
            ? error.join(", ")
            : t(
              "alert.modal.add.error",
              "An error occurred"
            );

      message.error(errorMessage);
    }
  }, [branchCode, addAlertState, dispatch, message, t]);

  const addCondition = () => {
    dispatch(actions.addCondition());
  };

  const addRecipient = () => {
    dispatch(actions.addRecipient());
  };

  return (
    <Modal
      title={t(
        addAlertState.mode === "update"
          ? "alert.modal.edit.title"
          : "alert.modal.create.title",
        addAlertState.mode === "update"
          ? "Edit Alert"
          : "Create New Alert"
      )}
      open={addAlertState.open}
      onCancel={onClose}
      onOk={onSubmit}
      width={800}
      className="dark:bg-gray-800"
      confirmLoading={addAlertState.submitting}
    >
      <AntForm layout="vertical">
        {/* Basic Information */}
        <Divider>
          {t(
            "alert.form.sections.basic",
            "Basic Information"
          )}
        </Divider>
        <AntForm.Item
          label={t("alert.form.name.label")}
          required
        >
          <Input
            placeholder={t(
              "alert.form.name.placeholder",
              "Enter alert name"
            )}
            value={addAlertState.name || ""}
            onChange={(e) =>
              dispatch(actions.setName(e.target.value))
            }
          />
        </AntForm.Item>

        <AntForm.Item
          label={t(
            "alert.form.description.label",
            "Description"
          )}
          required
        >
          <Input.TextArea
            rows={4}
            placeholder={t(
              "alert.form.description.placeholder",
              "Enter alert description"
            )}
            value={addAlertState.description || ""}
            onChange={(e) =>
              dispatch(
                actions.setDescription(e.target.value)
              )
            }
          />
        </AntForm.Item>

        <AntForm.Item
          label={t("alert.form.event.label")}
          required
        >
          <Select
            placeholder={t(
              "alert.form.event.placeholder",
              "Select event"
            )}
            value={addAlertState.event}
            onChange={(value) =>
              dispatch(
                actions.setEvent(value as EAlertEvent)
              )
            }
          >
            <Option value={EAlertEvent.ACTIVITY_SUBMITTED}>
              Activity Submitted
            </Option>
            <Option value={EAlertEvent.TASK_SUBMITTED}>
              Task Submitted
            </Option>
            <Option value={EAlertEvent.FORM_SUBMITTED}>
              Form Submitted
            </Option>
            <Option value={EAlertEvent.ALARM}>Alarm</Option>
            <Option value={EAlertEvent.CHECKPOINT_SCAN}>
              Checkpoint Scan
            </Option>
            <Option value={EAlertEvent.SIGN_ON}>
              Sign On
            </Option>
            <Option value={EAlertEvent.SIGN_OUT}>
              Sign Out
            </Option>
            <Option disabled={true} value={EAlertEvent.GEOFENCES}>
              Geofences
            </Option>
          </Select>
        </AntForm.Item>

        {/* Alert Conditions Group */}
        <Card className="shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg">
          <div className="flex items-center gap-2 text-gray-700 dark:text-gray-200 mb-4">
            <FilterOutlined/>
            <span className="text-base font-medium">
              {t(
                "alert.form.sections.conditions",
                "Alert Conditions"
              )}
            </span>
          </div>

          <AntForm.Item
            label={t(
              "alert.form.logicalCondition.label",
              "Logical Condition Type"
            )}
            required
          >
            <Select
              placeholder={t(
                "alert.form.logicalCondition.placeholder",
                "Select condition type"
              )}
              value={addAlertState.logicalConditionType}
              onChange={(value) =>
                dispatch(
                  actions.setLogicalConditionType(
                    value as EAlertLogicalConditionType
                  )
                )
              }
            >
              <Option value={EAlertLogicalConditionType.OR}>
                {t(
                  "alert.form.logicalCondition.or",
                  "Any Condition Can Be Met (OR)"
                )}
              </Option>
              <Option
                value={EAlertLogicalConditionType.AND}
              >
                {t(
                  "alert.form.logicalCondition.and",
                  "All Conditions Must Be Met (AND)"
                )}
              </Option>
            </Select>
          </AntForm.Item>

          <div className="space-y-4">
            {addAlertState.conditions.map((condition) => (
              <AlertConditionRow
                key={condition.rowUuid}
                rowId={condition.rowUuid}
                condition={condition}
              />
            ))}

            <div className="text-center mt-4">
              <Button
                type="dashed"
                onClick={addCondition}
                icon={<PlusOutlined/>}
                className="w-full"
              >
                {t(
                  "alert.form.addCondition",
                  "Add Condition"
                )}
              </Button>
            </div>
          </div>
        </Card>

        {/* Notification Settings Group */}
        <Card className="shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg mt-4">
          <div className="flex items-center gap-2 text-gray-700 dark:text-gray-200 mb-4">
            <MailOutlined/>
            <span className="text-base font-medium">
              {t(
                "alert.form.sections.notification",
                "Notification Settings"
              )}
            </span>
          </div>

          <AntForm.Item
            label={t("alert.form.action.label")}
            required
          >
            <Select
              placeholder={t(
                "alert.form.action.placeholder",
                "Select action"
              )}
              value={addAlertState.action}
              onChange={(value) =>
                dispatch(
                  actions.setAction(value as EAlertAction)
                )
              }
            >
              <Option value={EAlertAction.EMAIL}>
                Email
              </Option>
              <Option value={EAlertAction.SMS} disabled={true}>SMS</Option>
              <Option value={EAlertAction.DASHBOARD}>
                Dashboard
              </Option>
            </Select>
          </AntForm.Item>

          {
            addAlertState.action !== EAlertAction.DASHBOARD && (
              <>
                <AntForm.Item
                  label={t("alert.form.subject.label")}
                  required
                >
                  <Input
                    placeholder={t(
                      "alert.form.subject.placeholder",
                      "Enter message subject"
                    )}
                    value={addAlertState.subject || ""}
                    onChange={(e) =>
                      dispatch(actions.setSubject(e.target.value))
                    }
                  />
                </AntForm.Item>

                <AntForm.Item
                  label={t("alert.form.message.label")}
                  required
                >
                  <Input.TextArea
                    rows={4}
                    placeholder={t(
                      "alert.form.message.placeholder",
                      "Enter message content"
                    )}
                    value={addAlertState.message || ""}
                    onChange={(e) =>
                      dispatch(actions.setMessage(e.target.value))
                    }
                  />
                </AntForm.Item>

                <Divider>
                  {t(
                    "alert.form.sections.recipients",
                    "Recipients"
                  )}
                </Divider>
                <div className="space-y-4">
                  {addAlertState.recipients.map(
                    (recipient) => (
                      <AlertRecipientRow
                        key={recipient.rowUuid}
                        rowId={recipient.rowUuid}
                        recipient={recipient}
                      />
                    )
                  )}

                  <div className="text-center mt-4">
                    <Button
                      type="dashed"
                      onClick={addRecipient}
                      icon={<PlusOutlined/>}
                      className="w-full"
                    >
                      {t(
                        "alert.form.addRecipient",
                        "Add Recipient"
                      )}
                    </Button>
                  </div>
                </div>
              </>
            )
          }

        </Card>

        {/* Branch */}
        <Divider>
          {t("alert.form.sections.branches", "Branches")}
        </Divider>

        <AntForm.Item
          label={t("alert.form.branches.label")}
          required
        >
          <GridSelectBranch
            multiple
            value={addAlertState.branchIds}
            onChange={(value) => {
              if (Array.isArray(value)) {
                dispatch(actions.setBranchIds(value));
              } else {
                dispatch(actions.setBranchIds([value]));
              }
            }}
          />
        </AntForm.Item>

        {/* Active Status */}
        <AntForm.Item
          label={t("alert.form.active.label")}
          valuePropName="checked"
        >
          <Switch
            checked={addAlertState.active || false}
            onChange={(checked) =>
              dispatch(actions.setActive(checked))
            }
          />
        </AntForm.Item>
      </AntForm>
      {addAlertState.errorMessage && (
        <ErrorMessage
          message={addAlertState.errorMessage}
        />
      )}
    </Modal>
  );
};

export default ModalAddAlert;
