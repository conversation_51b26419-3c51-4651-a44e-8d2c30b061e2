import { useEffect, useRef, useCallback, useMemo, useState } from "react";
import Map, { MapRef } from "../../../../components/Map/Map";
import { useParams } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "../../../../store/hooks";
import dashboardMapAdminSlice, { fetchDashboardData, fetchGeofencesDashboard } from "../../../../store/slices/admin/dashboardMap.admin.slice.ts";
import ActivityList from "./ActivityList";
import ActiveUserList from "./ActivityList/ActiveUserList";
import DashboardMapFilterModal from "./DashboardMapFilterModal";

interface DashboardMapProps {
  isActive?: boolean;
}

const DashboardMap = ({ isActive = true }: DashboardMapProps) => {
  const mapRef = useRef<MapRef>(null);
  const { branchCode } = useParams<{ branchCode: string }>();
  const dispatch = useAppDispatch();
  const {
    geofences,
    fetchingGeofences,
    dashboardData,
    dashboardMeta,
    fetchingDashboardData,
    error,
    filterModal
  } = useAppSelector(
    (state) => state.dashboardMapAdmin
  );

  const [initialLoading, setInitialLoading] = useState(true);
  const isFetching = useRef(false);
  const isInitialFetch = useRef(true);
  const geofenceFetching = useRef(false);

  const fetchDashboard = useCallback(async () => {
    if (isFetching.current || !branchCode) return;

    try {
      isFetching.current = true;
      await dispatch(fetchDashboardData());
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
    } finally {
      if (isInitialFetch.current) {
        setInitialLoading(false);
        isInitialFetch.current = false;
      }
      isFetching.current = false;
    }
  }, [branchCode, dispatch]);

  const fetchGeofences = useCallback(async () => {
    if (geofenceFetching.current || !branchCode) return;

    try {
      geofenceFetching.current = true;
      await dispatch(fetchGeofencesDashboard());
    } catch (error) {
      console.error("Error fetching geofences:", error);
    } finally {
      geofenceFetching.current = false;
    }
  }, [branchCode, dispatch]);

  useEffect(() => {
    if (isActive) {
      // Fetch geofences only once
      if (!geofences.length) {
        fetchGeofences();
      }
      
      // Fetch dashboard data
      fetchDashboard();
      
      // Set up interval for dashboard data only
      const intervalId = setInterval(() => {
        fetchDashboard();
      }, 20000);
      
      return () => {
        clearInterval(intervalId);
      };
    }
  }, [isActive, fetchDashboard, fetchGeofences, geofences.length]);

  const transformedGeofences = useMemo(() => {
    return geofences.map(geofence => {
      const coordinates = geofence.geofence_data.coordinates;
      return {
        id: geofence.id,
        geofence: coordinates as [number, number][],
      };
    });
  }, [geofences]);

  // Combine all activities from dashboard data for ActivityList
  const activitiesForList = useMemo(() => {
    if (!dashboardData) return [];
    
    return [
      ...(dashboardData.checkpoint || []).map(item => ({
        id: item.uuid,
        userName: item.user_name,
        eventId: item.uuid,
        eventName: item.checkpoint_name,
        eventSubmittedTime: item.original_submitted_time,
        latitude: item.latitude,
        longitude: item.longitude,
        type: 'checkpoint' as const,
      })),
      ...(dashboardData.activity || []).map(item => ({
        id: item.uuid,
        userName: item.user_name,
        eventId: item.uuid,
        eventName: item.activity_name,
        eventSubmittedTime: item.original_submitted_time,
        latitude: item.latitude,
        longitude: item.longitude,
        type: 'activity' as const,
      })),
      ...(dashboardData.alarm || []).map(item => ({
        id: item.uuid,
        userName: item.user_name,
        eventId: item.uuid,
        eventName: item.alarm_message || 'Alarm',
        eventSubmittedTime: item.original_submitted_time,
        latitude: item.latitude,
        longitude: item.longitude,
        type: 'alarm' as const,
      })),
      ...(dashboardData.form || []).map(item => ({
        id: item.uuid,
        userName: item.user_name,
        eventId: item.uuid,
        eventName: item.form_name,
        eventSubmittedTime: item.original_submitted_time,
        latitude: item.latitude,
        longitude: item.longitude,
        type: 'form' as const,
      })),
      ...(dashboardData.task || []).map(item => ({
        id: item.uuid,
        userName: item.user_name,
        eventId: item.uuid,
        eventName: item.task_name,
        eventSubmittedTime: item.original_submitted_time,
        latitude: item.latitude,
        longitude: item.longitude,
        type: 'task' as const,
      })),
      ...(dashboardData.geofence || []).map(item => ({
        id: item.uuid,
        userName: item.user_name,
        eventId: item.uuid,
        eventName: item.geofence_name,
        eventSubmittedTime: item.original_submitted_time,
        latitude: item.latitude,
        longitude: item.longitude,
        type: 'geofence' as const,
      })),
      ...(dashboardData.sign_in_out || []).map(item => ({
        id: item.uuid,
        userName: item.user_name,
        eventId: item.uuid,
        eventName: `${item.user_log_type}`,
        eventSubmittedTime: item.event_time,
        latitude: item.latitude,
        longitude: item.longitude,
        type: 'sign_in_out' as const,
      }))
    ];
  }, [dashboardData]);

  // Transform activities for map markers
  const transformedActivities = useMemo(() => {
    return activitiesForList
      .filter(activity => activity.latitude && activity.longitude)
      .map(activity => ({
        id: activity.id,
        position: [activity.latitude, activity.longitude] as [number, number],
        title: activity.userName,
        description: activity.eventName,
        date: activity.eventSubmittedTime,
        type: activity.type,
      }));
  }, [activitiesForList]);

  const handleUserActivityClick = (id: string) => {
    mapRef.current?.goToMarkerById(id);
  };

  const handleToggleFilterModal = () => {
    dispatch(dashboardMapAdminSlice.actions.toggleFilterModal());
  };

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm h-[calc(100vh-180px)] relative">

      <div className="absolute top-10 right-10 z-[1000] w-80">
        <ActiveUserList
          activeUsers={dashboardData?.active_users || []}
          loading={initialLoading}
          branchTimezone={dashboardMeta?.branch_timezone_name}
        />
        <ActivityList
          activities={activitiesForList}
          loading={initialLoading}
          onActivityClick={handleUserActivityClick}
          branchTimezone={dashboardMeta?.branch_timezone_name}
        />
      </div>

      <DashboardMapFilterModal
        visible={filterModal.visible}
        onClose={handleToggleFilterModal}
      />

      <div className="map-container h-full relative">
        {error && (
          <div className="absolute top-0 left-0 right-0 z-20 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {Array.isArray(error) ? error.join(', ') : error}
          </div>
        )}
        
        {/* Loading indicator - only show for dashboard data, not blocking map */}
        {fetchingDashboardData && !initialLoading && (
          <div className="absolute top-4 right-4 z-20 bg-white bg-opacity-90 rounded-lg px-3 py-2 shadow-md">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
              <span className="text-sm text-gray-600">Updating...</span>
            </div>
          </div>
        )}

        {/* Map - always rendered once geofences are loaded */}
        {!fetchingGeofences && (
          <Map
            ref={mapRef}
            geofences={transformedGeofences}
            checkpointActivityMarkers={transformedActivities}
            geolocationActivityMarkers={[]}
            geofenceActivities={dashboardData?.geofence || []}
          />
        )}

        {/* Initial loading for geofences and initial dashboard data */}
        {(fetchingGeofences && !geofences.length) || initialLoading ? (
          <div className="absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center z-10">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <span className="text-gray-600">Loading map...</span>
            </div>
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default DashboardMap;
