import { Avatar, List, Spin } from "antd";
import { UserOutlined, DownOutlined, UpOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useState } from "react";
import { convertToBranchTimezone } from "../../../../../utils/dateUtils.ts";

// Define the interface for active user data
interface ActiveUser {
    token_id: number;
    user_id: string;
    user_name: string;
    device_name: string;
    device_uid: string;
    user_agent: string;
    last_used_at: string;
    expires_at: string;
    created_at: string;
}

interface ActiveUserListProps {
    activeUsers: ActiveUser[];
    loading: boolean;
    branchTimezone?: string;
}

const ActiveUserList: React.FC<ActiveUserListProps> = ({
    activeUsers,
    loading,
    branchTimezone,
}) => {
    const { t } = useTranslation();
    const [isExpanded, setIsExpanded] = useState(true); // Default expanded

    const toggleExpanded = () => {
        setIsExpanded(!isExpanded);
    };



    return (
        <div
            className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden mb-4">
            <div
                className="p-3 border-b border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                onClick={toggleExpanded}
            >
                <div className="flex justify-between items-center">
                    <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                        {t("dashboard.map.title")}
                    </h3>
                    <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                            {t("dashboard.map.totalUsers", {
                                count: activeUsers.length,
                            })}
                        </span>
                        {isExpanded ? (
                            <UpOutlined className="text-gray-500 dark:text-gray-400 text-xs" />
                        ) : (
                            <DownOutlined className="text-gray-500 dark:text-gray-400 text-xs" />
                        )}
                    </div>
                </div>
            </div>

            {isExpanded && (
                <div className="p-2">
                    {loading && (
                        <div className="flex justify-center items-center py-4">
                            <Spin size="small" />
                            <span className="ml-2 text-sm text-gray-500">Loading active users...</span>
                        </div>
                    )}

                    {!loading && activeUsers.length === 0 && (
                        <div className="text-center py-3 text-sm text-gray-500 dark:text-gray-400">
                            No active users found
                        </div>
                    )}

                    {!loading && activeUsers.length > 0 && (
                        <List
                            itemLayout="horizontal"
                            dataSource={activeUsers}
                            className="active-user-list"
                            size="small"
                            renderItem={(user) => (
                                <List.Item
                                    className="rounded-md px-2 py-1.5 mb-1.5 transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-700"
                                >
                                    <List.Item.Meta
                                        avatar={
                                            <Avatar
                                                size="small"
                                                icon={<UserOutlined />}
                                                className="bg-green-500"
                                            />
                                        }
                                        title={
                                            <span className="text-sm font-medium text-gray-900 dark:text-gray-100 line-clamp-1">
                                                {user.user_name}
                                            </span>
                                        }
                                        description={
                                            <div className="text-xs text-gray-500 dark:text-gray-400 space-y-0.5">
                                                <div className="line-clamp-1">{user.device_name}</div>
                                                <div className="text-xs text-gray-400 dark:text-gray-500">
                                                    Last Active: {convertToBranchTimezone(user.created_at, branchTimezone)?.format("DD MMM YYYY HH:mm")}
                                                </div>
                                            </div>
                                        }
                                    />
                                    <div className="flex items-center">
                                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                    </div>
                                </List.Item>
                            )}
                        />
                    )}
                </div>
            )}
        </div>
    );
};

export default ActiveUserList; 