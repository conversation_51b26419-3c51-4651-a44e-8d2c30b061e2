import { Avatar, List, Spin } from "antd";
import { UserOutlined, DownOutlined, UpOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useState } from "react";
import { convertToBranchTimezone } from "../../../../../utils/dateUtils.ts";

// Define the interface for checkpoint activity data
interface CheckpointActivity {
  id: string;
  userName: string;
  eventId: string;
  eventName: string;
  eventSubmittedTime: string;
  latitude: number | null;
  longitude: number | null;
}

interface ActivityListProps {
  activities: CheckpointActivity[];
  loading: boolean;
  onActivityClick: (id: string) => void;
  branchTimezone?: string;
}

const ActivityList: React.FC<ActivityListProps> = ({
  activities,
  loading,
  onActivityClick,
  branchTimezone,
}) => {
  const { t } = useTranslation();
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };



  return (
    <div
      className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div
        className="p-3 border-b border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
        onClick={toggleExpanded}
      >
        <div className="flex justify-between items-center">
          <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
            {t("dashboard.activity.title")}
          </h3>
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {t("dashboard.activity.totalUsers", {
                count: activities.length,
              })}
            </span>
            {isExpanded ? (
              <UpOutlined className="text-gray-500 dark:text-gray-400 text-xs" />
            ) : (
              <DownOutlined className="text-gray-500 dark:text-gray-400 text-xs" />
            )}
          </div>
        </div>
      </div>

      {isExpanded && (
        <div className="p-2">
          {loading && (
            <div className="flex justify-center items-center py-4">
              <Spin size="small" />
              <span className="ml-2 text-sm text-gray-500">Loading activities...</span>
            </div>
          )}

          {!loading && activities.length === 0 && (
            <div className="text-center py-3 text-sm text-gray-500 dark:text-gray-400">
              No activities found
            </div>
          )}

          {!loading && activities.length > 0 && (
            <List
              itemLayout="horizontal"
              dataSource={activities}
              className="user-activity-list"
              size="small"
              renderItem={(item) => (
                <List.Item
                  className="rounded-md px-2 py-1.5 mb-1.5 cursor-pointer transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-700"
                  onClick={() => onActivityClick(item.id)}
                >
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        size="small"
                        icon={<UserOutlined />}
                        className="bg-blue-500"
                      />
                    }
                    title={
                      <span className="text-sm font-medium text-gray-900 dark:text-gray-100 line-clamp-1">
                        {item.userName}
                      </span>
                    }
                    description={
                      <div className="text-xs text-gray-500 dark:text-gray-400 space-y-0.5">
                        <div className="line-clamp-1">{item.eventName}</div>
                        <div className="text-xs text-gray-400 dark:text-gray-500">
                          {convertToBranchTimezone(item.eventSubmittedTime, branchTimezone)?.format("DD MMM YYYY HH:mm")}
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default ActivityList;
