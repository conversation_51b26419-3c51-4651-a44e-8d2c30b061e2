import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import relativeTime from "dayjs/plugin/relativeTime";

// Initialize dayjs plugins
dayjs.extend(utc);
dayjs.extend(relativeTime);
dayjs.extend(timezone);

/**
 * Format a date string to a readable format with time (DD MMM YYYY HH:mm)
 * @param dateString ISO date string
 * @returns Formatted date string
 */
export const formatDateWithTimeUTC = (dateString: string): string => {
  try {
    return dayjs(dateString).utc().format("DD MMM YYYY HH:mm");
  } catch {
    return dateString;
  }
};

/**
 * Converts UTC date time string to local timezone for display
 * @param utcDateString UTC date time string (e.g. "2025-02-26T01:00:00.000Z")
 * @returns dayjs object adjusted to local timezone
 */
export const convertToLocalTimezone = (utcDateString: string | null | undefined) => {
  if (!utcDateString) return undefined;
  return dayjs(utcDateString); // dayjs automatically handles timezone conversion for display
};

/**
 * Converts UTC date time string to specific branch timezone for display
 * @param utcDateString UTC date time string (e.g. "2025-02-26T01:00:00.000Z")
 * @param branchTimezone Branch timezone name (e.g. "Asia/Jakarta")
 * @returns dayjs object adjusted to branch timezone, fallback to local timezone if branchTimezone not provided
 */
export const convertToBranchTimezone = (utcDateString: string | null | undefined, branchTimezone?: string | null) => {
  if (!utcDateString) return undefined;
  
  if (!branchTimezone) {
    return convertToLocalTimezone(utcDateString);
  }
  
  return dayjs.utc(utcDateString).tz(branchTimezone);
};

/**
 * Converts local date time back to UTC format for storing in the database
 * @param localDate dayjs object in local timezone
 * @returns UTC date time string in format "YYYY-MM-DD HH:mm:00"
 */
export const convertToUTC = (localDate: dayjs.Dayjs | null) => {
  if (!localDate) return null;
  return localDate.utc().toISOString();
};

/**
 * Safe format with dayjs that handles null values
 * @param dateStr Date string or null
 * @param format Format string for dayjs
 * @param timeZoneName
 * @returns Formatted date string or fallback
 */
export const formatDateTime = (
  dateStr: string | null,
  format: string,
  timeZoneName?: string | null
): string => {
  if (!dateStr) return "-";
  try {
    let date = dayjs(dateStr);
    if (timeZoneName) {
      date = date.tz(timeZoneName);
    }
    return date.format(format);
  } catch {
    // Ignore format errors, return the original string
    return dateStr || "-";
  }
};

/**
 * Safe fromNow function that handles null values
 * @param dateStr Date string or null
 * @param timeZoneName
 * @returns Relative time string (e.g. "2 hours ago") or fallback
 */
export const formatTimeAgo = (
  dateStr: string | null,
  timeZoneName?: string | null
): string => {
  if (!dateStr) return "-";
  try {
    let date = dayjs(dateStr);
    if (timeZoneName) {
      date = date.tz(timeZoneName);
    }
    return date.fromNow();
  } catch {
    // Ignore format errors, return the original string
    return dateStr || "-";
  }
};
