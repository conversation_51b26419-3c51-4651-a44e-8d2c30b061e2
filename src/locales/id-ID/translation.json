{"dashboard": {"title": "Dasbor Aktivitas", "activity": {"title": "Aktivitas Pengguna Terbaru", "totalUsers": "Total Aktivitas: {{count}}"}, "map": {"title": "Pengguna Aktif", "totalUsers": "Pengguna Aktif: {{count}}"}, "tabs": {"activity": "AKTIVITAS", "maps": "PETA"}}, "branch": {"title": "Manajemen Klien", "description": "Kelola cabang organisasi Anda", "addButton": "Tambah Klien Baru", "search": {"placeholder": "Cari cabang...", "sortOptions": {"createdAt": "Tanggal Dibuat", "branchName": "<PERSON><PERSON>"}, "sortDirection": {"asc": "Menaik", "desc": "<PERSON><PERSON><PERSON>"}}, "grid": {"empty": "Tidak ada cabang ditemukan", "total": "Total {{total}} item", "status": {"inactive": "Tidak Aktif", "mainBranch": "Cabang <PERSON>"}, "license": "<PERSON><PERSON><PERSON>", "columns": {"name": "<PERSON><PERSON>", "code": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "phone": "Telepon", "createdAt": "Dibuat Pada", "updatedAt": "<PERSON><PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>"}, "actions": {"view": "Lihat", "edit": "Edit", "delete": "Hapus"}}, "modal": {"add": {"title": "Tambah Cabang Baru", "success": "Cabang ber<PERSON><PERSON> di<PERSON>", "error": "Gagal menambahkan cabang"}, "edit": {"title": "<PERSON>", "success": "Cabang ber<PERSON><PERSON>", "error": "<PERSON><PERSON> me<PERSON> cabang"}, "view": {"title": "Detail Cabang", "edit": "<PERSON>", "delete": "<PERSON><PERSON>", "confirmDelete": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus cabang ini?"}, "form": {"name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON>n nama cabang", "required": "<PERSON><PERSON><PERSON> masukkan nama cabang"}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON>ripsi cabang"}, "license": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> lise<PERSON>i", "required": "<PERSON><PERSON><PERSON> pilih lisensi"}, "timezone": {"label": "Zona Waktu", "placeholder": "<PERSON><PERSON>h zona waktu", "required": "<PERSON><PERSON>an pilih zona waktu"}, "status": {"label": "Status", "active": "Aktif", "inactive": "Tidak Aktif"}}, "sections": {"branchInfo": "Informasi Cabang", "licenseDetails": "<PERSON><PERSON>", "timezoneConfig": "Konfigurasi Zona Waktu", "additionalInfo": "<PERSON><PERSON>", "adminInfo": "Informasi Admin <PERSON>"}, "admin": {"name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama admin", "required": "<PERSON><PERSON><PERSON> masukkan nama admin"}, "email": {"label": "Email", "placeholder": "<PERSON><PERSON><PERSON><PERSON> email", "required": "<PERSON><PERSON><PERSON> ma<PERSON> email", "invalid": "<PERSON><PERSON><PERSON> ma<PERSON>kkan email yang valid"}, "phone": {"label": "Nomor Telepon", "placeholder": "Ma<PERSON>kkan nomor telepon", "required": "<PERSON><PERSON>an masukkan nomor telepon", "invalid": "<PERSON><PERSON>an masukkan nomor telepon yang valid"}, "password": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> kata sandi", "placeholderUpdate": "<PERSON><PERSON>kka<PERSON> kata sandi baru (biarkan kosong jika tidak ingin mengubah)", "required": "<PERSON><PERSON><PERSON> masukkan kata sandi", "minLength": "<PERSON><PERSON> sandi harus minimal 6 karakter", "optionalHint": "Biarkan kosong jika tidak ingin mengubah kata sandi"}, "confirmPassword": {"label": "<PERSON>n<PERSON><PERSON><PERSON>", "placeholder": "Konfirmasi kata sandi", "placeholderUpdate": "Konfirmasi kata sandi baru (biarkan kosong jika tidak ingin mengu<PERSON>)", "required": "<PERSON><PERSON><PERSON> konfirmasi kata sandi", "mismatch": "Kata sandi tidak cocok"}}}}, "roles": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> peran organisa<PERSON>", "addButton": "Tambah Peran Baru", "search": {"placeholder": "<PERSON>i peran...", "sortOptions": {"createdAt": "Tanggal Dibuat", "roleName": "<PERSON><PERSON>", "updatedAt": "<PERSON><PERSON>"}, "sortDirection": {"asc": "Menaik", "desc": "<PERSON><PERSON><PERSON>"}}, "modal": {"add": {"title": "Tambah Peran Baru", "success": "<PERSON><PERSON> be<PERSON> di<PERSON>at", "error": "<PERSON><PERSON> membuat peran"}, "edit": {"title": "<PERSON>", "success": "<PERSON><PERSON>", "error": "<PERSON><PERSON> peran"}, "view": {"title": "<PERSON><PERSON>"}, "form": {"name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama peran", "required": "<PERSON><PERSON> peran diper<PERSON>an"}}, "sections": {"roleInfo": "Informasi Peran", "permissions": "<PERSON><PERSON>", "additionalInfo": "<PERSON><PERSON>"}, "permissions": {"module": {"title": "<PERSON><PERSON>", "label": "⚡ <PERSON><PERSON>"}, "report": {"title": "<PERSON><PERSON>", "label": "📊 <PERSON><PERSON>"}, "checkAll": "<PERSON><PERSON><PERSON>"}, "status": {"label": "Status", "active": "Aktif", "inactive": "Tidak Aktif"}, "fields": {"parentBranch": "Cabang Induk", "createdAt": "Dibuat Pada"}}, "table": {"roleName": "<PERSON><PERSON>", "status": "Status", "createdAt": "Dibuat Pada", "action": "<PERSON><PERSON><PERSON>", "edit": "Edit", "view": "Lihat", "editTooltip": "<PERSON>", "viewTooltip": "<PERSON><PERSON>"}, "permissions": {"name": "<PERSON><PERSON>", "view": "Lihat", "create": "Buat", "update": "<PERSON><PERSON><PERSON>", "delete": "Hapus", "modulePermissions": "<PERSON><PERSON>", "reportPermissions": "<PERSON><PERSON>"}}, "menu": {"dashboard": "Dashboard", "branch": "<PERSON><PERSON><PERSON>", "license": "<PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON><PERSON>", "activity": "Aktivitas", "task": "Tugas", "alert": "Peringatan", "scheduler": "<PERSON><PERSON><PERSON><PERSON>", "forms": "<PERSON><PERSON><PERSON>", "forms-picklist": "<PERSON><PERSON><PERSON>", "forms-group": "<PERSON><PERSON><PERSON>", "field-monitoring": "Pemantauan <PERSON>", "monitoring-group": "Pemantauan <PERSON>", "monitoring-site": "<PERSON><PERSON>", "site": "<PERSON><PERSON>", "geofence": "Geofence", "checkpoint": "<PERSON><PERSON>", "analytics": "<PERSON><PERSON><PERSON>", "analytics-group": "<PERSON><PERSON><PERSON>", "activity-log": "Log Aktivitas", "alarms-log": "Alarm", "average-rotation-log": "<PERSON><PERSON><PERSON>a", "branch-details-log": "Detail Cabang", "checkpoint-activity-log": "Aktivitas Pos Pemeriksaan", "checkpoint-battery-log": "Baterai Pos Pemeriksaan", "exception-log": "Pengecualian", "exception-detailed-log": "Detail <PERSON>", "forms-log": "<PERSON><PERSON><PERSON>", "analytics-forms-log": "<PERSON><PERSON><PERSON>", "geofence-log": "Geofence", "analytics-geofence-log": "Geofence", "missed-zone-log": "Zona Terlewat", "sign-on-off-log": "Masuk/Keluar", "gps-heatmap-log": "Peta Panas GPS", "tasks-log": "Tugas-tugas", "analytics-tasks-log": "Tugas-tugas", "time-rotation-log": "Waktu & Rotasi", "time-on-zone-log": "Waktu di Zona", "unknown-log": "Data Tidak Diketahui", "settings-group": "<PERSON><PERSON><PERSON><PERSON>", "client": "<PERSON><PERSON><PERSON>", "roles": "<PERSON><PERSON>", "users": "Pengguna", "label": "Label", "device": "<PERSON><PERSON><PERSON>", "beacon": "Beacon"}, "users": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Ke<PERSON>la pengguna organisasi Anda", "addButton": "Tambah Pengguna Baru", "search": {"placeholder": "<PERSON>i pengguna...", "sortOptions": {"createdAt": "Tanggal Dibuat", "name": "<PERSON><PERSON>", "email": "Email"}, "sortDirection": {"asc": "Menaik", "desc": "<PERSON><PERSON><PERSON>"}}, "userItem": {"inactive": "Tidak Aktif", "roleAccess": "Peran & Akses", "role": "<PERSON><PERSON>", "access": "<PERSON><PERSON><PERSON>", "noRole": "Tidak Ada Peran", "branchAssignment": "<PERSON><PERSON><PERSON>", "branch": "Cabang", "branches": "Cabang", "noBranchesAssigned": "Tidak ada cabang yang ditugaskan", "contactInfo": "Info Kontak", "noPhoneNumber": "Tidak ada nomor telepon", "created": "Dibuat", "web": "Web", "mobile": "Mobile"}, "modal": {"add": {"title": "Tambah Pengguna Baru"}, "edit": {"title": "<PERSON>"}, "saveSuccess": "Pengguna berhasil disimpan", "form": {"email": {"label": "Email", "placeholder": "Masukkan email"}, "name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama"}, "phone": {"label": "Telepon", "placeholder": "Ma<PERSON>kkan nomor telepon"}, "password": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> kata sandi", "placeholderUpdate": "<PERSON><PERSON>kkan kata sandi baru"}, "confirmPassword": {"label": "<PERSON>n<PERSON><PERSON><PERSON>", "placeholder": "Konfirmasi kata sandi", "placeholderUpdate": "Konfirmasi kata sandi baru"}, "allowMobileAccess": "Izinkan Akses Mobile", "allowWebAccess": "Izinkan Akses Web", "role": {"label": "<PERSON><PERSON><PERSON>"}, "label": {"label": "Pilih Label"}, "client": {"label": "<PERSON><PERSON><PERSON>"}}, "table": {"name": "<PERSON><PERSON>", "email": "Email", "phone": "Telepon", "role": "<PERSON><PERSON>", "status": "Status", "actions": "<PERSON><PERSON><PERSON>"}, "status": {"active": "Aktif", "inactive": "Tidak Aktif"}, "actions": {"edit": "Edit", "view": "Lihat", "delete": "Hapus"}}, "list": {"noUsersFound": "Tidak ada pengguna yang di<PERSON>ukan", "total": "Total {{total}} item"}}, "label": {"title": "Manajemen Label", "description": "Kelola label organisasi Anda", "addButton": "Tambah Label Baru", "search": {"placeholder": "Cari label...", "sortOptions": {"createdAt": "Tanggal Dibuat", "labelName": "<PERSON><PERSON>", "labelDescription": "<PERSON><PERSON><PERSON><PERSON>"}, "sortDirection": {"asc": "Menaik", "desc": "<PERSON><PERSON><PERSON>"}}, "modal": {"add": {"title": "Tambah Label Baru", "success": "Label berhasil dibuat", "error": "Gagal membuat label"}, "edit": {"title": "Edit Label", "success": "Label berhasil diperbarui", "error": "Gagal memperbarui label"}, "form": {"name": {"label": "Nama Label", "placeholder": "Masukkan nama label", "required": "<PERSON>lakan masukkan nama label"}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "Masukkan deskripsi label", "required": "<PERSON>lakan masukkan deskripsi label"}}}}, "mainLayout": {"darkMode": "Mode Gelap", "changeClient": "Ganti Klien", "selectClient": "<PERSON><PERSON><PERSON>", "appName": "<PERSON><PERSON><PERSON>", "defaultBranch": "Admin"}, "layout": {"webAdmin": "Admin Web"}, "selectBranch": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> / <PERSON><PERSON>n untuk <PERSON>", "searchPlaceholder": "Cari cabang...", "errors": {"noPermission": "Anda tidak memiliki izin untuk mengakses klien ini", "verifyFailed": "Gagal memverifikasi akses cabang"}}, "activity": {"title": "Manajemen Aktivitas", "description": "Kelola dan lacak aktivitas di organisasi Anda", "addNew": "Tambah Aktivitas Baru", "search": {"placeholder": "Cari aktivitas...", "sortOptions": {"createdAt": "Tanggal Dibuat", "name": "Nama Aktivitas"}}, "table": {"name": "Nama Aktivitas", "description": "<PERSON><PERSON><PERSON><PERSON>", "requirements": "Persyara<PERSON>", "status": {"active": "Aktif", "inactive": "Tidak Aktif"}, "gpsRequired": "GPS Diperlukan", "photoRequired": "Foto Diperlukan", "commentRequired": "<PERSON><PERSON><PERSON>", "active": "Aktif", "inactive": "Tidak Aktif"}, "form": {"name": "Nama Aktivitas", "description": "<PERSON><PERSON><PERSON><PERSON>", "requirements": "Persyara<PERSON>", "status": "Status", "active": "Aktif", "gpsRequired": "GPS Diperlukan", "photoRequired": "Foto Diperlukan", "commentRequired": "<PERSON><PERSON><PERSON>", "nameRequired": "<PERSON><PERSON><PERSON> masukkan nama aktivitas", "descriptionRequired": "<PERSON><PERSON><PERSON> ma<PERSON>kkan deskripsi aktivitas"}, "modal": {"add": {"title": "Buat Aktivitas Baru", "success": "Aktivitas berhasil dibuat", "error": "Gagal membuat aktivitas"}, "edit": {"title": "Edit Aktivitas", "success": "Aktivitas berhasil diperbarui", "error": "Gagal memperbarui aktivitas"}}, "list": {"empty": "Tidak ada aktivitas ditemukan", "createdAt": "Dibuat"}, "noDescription": "Tidak ada <PERSON>", "logTitle": "Log Aktivitas", "type": "Aktivitas", "location": "<PERSON><PERSON>", "dateTime": "Tanggal/Waktu", "comment": "Komentar", "id": "ID", "eventTime": "<PERSON><PERSON><PERSON>"}, "scheduler": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> dan pantau semua aktivitas penjadwalan", "addButton": "<PERSON><PERSON> Jadwal", "search": {"placeholder": "<PERSON><PERSON> jadwal..."}, "sort": {"createdAt": "Tanggal Dibuat", "name": "<PERSON><PERSON>"}, "list": {"noSchedulersFound": "Tidak ada jadwal yang di<PERSON>n", "total": "Total {{total}} item"}, "item": {"inactive": "Tidak Aktif", "noDescription": "Tidak ada <PERSON>", "reportDetails": "Detail <PERSON>", "type": "Tipe", "format": "Format", "scheduleSettings": "<PERSON><PERSON><PERSON><PERSON>", "frequency": "<PERSON><PERSON><PERSON><PERSON>", "period": "Periode", "time": "<PERSON><PERSON><PERSON>", "assignment": "<PERSON><PERSON><PERSON>", "user": "Pengguna", "noUser": "Tidak ada pengguna yang ditugaskan", "branch": "Cabang", "noBranch": "Tidak ada cabang yang dipilih", "executionInfo": "<PERSON><PERSON>", "nextRun": "Eksekusi be<PERSON>", "noScheduledRun": "Tidak ada jadwal eksekusi", "generatedOn": "Dibuat pada", "created": "Dibuat", "edit": "Edit", "delete": "Hapus", "additionalFilters": "<PERSON><PERSON>", "userLabels": "Label Pengguna", "deviceLabels": "Label Perangkat", "zoneLabels": "Label Zona", "checkpointLabels": "Label Checkpoint"}, "modal": {"title": {"add": "Tambah Penjadwal Baru", "edit": "<PERSON>"}, "addTitle": "Tambah Penjadwal Baru", "editTitle": "<PERSON>", "sections": {"basic": {"title": "Informasi <PERSON>", "tooltip": "<PERSON>i detail dasar penja<PERSON>wal <PERSON>a"}, "basicTitle": "Informasi <PERSON>", "basicTooltip": "<PERSON>i detail dasar penja<PERSON>wal <PERSON>a", "schedule": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tooltip": "Konfigurasi kapan laporan harus dibuat"}, "filter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tooltip": "Konfigurasi pengaturan email untuk pengiriman laporan"}}, "form": {"name": {"label": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON><PERSON> nama <PERSON>rip<PERSON>f untuk penjadwal Anda", "placeholder": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>"}, "nameLabel": "<PERSON><PERSON>", "nameTooltip": "<PERSON><PERSON><PERSON><PERSON> nama <PERSON>rip<PERSON>f untuk penjadwal Anda", "namePlaceholder": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "description": {"label": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Berikan detail tentang tujuan penja<PERSON>wal", "placeholder": "<PERSON><PERSON><PERSON> tujuan dan penggunaan penjadwal ini..."}, "descriptionLabel": "<PERSON><PERSON><PERSON><PERSON>", "descriptionTooltip": "Berikan detail tentang tujuan penja<PERSON>wal", "descriptionPlaceholder": "<PERSON><PERSON><PERSON> tujuan dan penggunaan penjadwal ini...", "reportType": {"label": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> jenis laporan yang akan dibuat"}, "frequency": {"label": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> seberapa sering laporan ini harus dibuat"}, "reportFormat": {"label": "Format Laporan", "tooltip": "Pilih format untuk laporan yang dibuat", "options": {"pdf": "PDF", "csv": "CSV", "excel": "Excel"}}, "period": {"start": {"label": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> tanggal mulai untuk penjadwal ini"}, "end": {"label": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> tanggal akhir untuk penjadwal ini"}}, "generate": {"date": {"label": "Tanggal Pembuatan", "tooltip": "<PERSON><PERSON><PERSON> tanggal kapan laporan harus dibuat"}, "time": {"label": "W<PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> waktu kapan laporan harus dibuat"}}, "generateDate": {"label": "Tanggal Pembuatan", "tooltip": "<PERSON><PERSON><PERSON> tanggal kapan laporan harus dibuat"}, "generateTime": {"label": "W<PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> waktu kapan laporan harus dibuat"}, "stopIfBlank": {"label": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON><PERSON> membuat jika tidak ada data tersedia"}, "detailedReport": {"label": "<PERSON><PERSON><PERSON>", "tooltip": "Sertakan informasi terperinci dalam laporan"}, "active": {"label": "Aktif", "tooltip": "Aktifkan atau nonaktifkan penjadwal ini"}, "startTime": {"label": "<PERSON><PERSON><PERSON>", "tooltip": "Tetapkan waktu dari mana data harus diambil"}, "endTime": {"label": "<PERSON><PERSON><PERSON>", "tooltip": "Tetapkan waktu sampai kapan data harus diambil"}, "selectedBranch": {"label": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON>lih cabang untuk penjadwal ini"}, "selectAll": {"label": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> semua cabang yang tersedia"}, "subject": {"label": "<PERSON>jek <PERSON>", "tooltip": "Masukkan baris subjek untuk email", "placeholder": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> un<PERSON> {{date}}"}, "message": {"label": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON><PERSON> isi pesan untuk email", "placeholder": "<PERSON><PERSON><PERSON><PERSON> konten pesan email..."}, "email": {"label": "<PERSON><PERSON><PERSON>", "tooltip": "Tambahkan alamat email untuk menerima laporan", "placeholder": "<PERSON><PERSON><PERSON><PERSON> email", "addButton": "Tambah"}}}}, "alert": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Pantau dan kelola peringatan sistem", "addButton": "Tambah Peringatan", "search": {"placeholder": "<PERSON>i per<PERSON>...", "sortOptions": {"createdAt": "Tanggal Dibuat", "name": "<PERSON><PERSON>"}}, "filter": {"all": "<PERSON><PERSON><PERSON>"}, "status": {"critical": "<PERSON><PERSON><PERSON>", "warning": "Peringatan", "normal": "Normal", "inactive": "Tidak Aktif"}, "labels": {"event": "<PERSON><PERSON><PERSON><PERSON>", "action": "<PERSON><PERSON><PERSON>", "createdAt": "Dibuat", "updatedAt": "<PERSON><PERSON><PERSON>"}, "list": {"empty": "Tidak ada peringatan ditemukan"}, "form": {"name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama peringatan"}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>"}, "event": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON>"}, "logicalCondition": {"label": "<PERSON><PERSON><PERSON>"}, "subject": {"label": "Subjek"}, "message": {"label": "<PERSON><PERSON>"}, "action": {"label": "<PERSON><PERSON><PERSON>"}, "sections": {"basic": "Informasi <PERSON>", "conditions": "<PERSON><PERSON><PERSON>", "recipients": "<PERSON><PERSON><PERSON>", "branches": "Cabang"}, "branches": {"label": "Cabang"}, "active": {"label": "Aktif"}}, "modal": {"create": {"title": "Buat Peringatan Baru"}, "edit": {"title": "<PERSON>"}, "add": {"success": "Peringatan berhasil dibuat", "error": "<PERSON><PERSON><PERSON><PERSON>"}, "update": {"success": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>"}}, "error": {"noBranchCode": "Kode cabang diperlukan", "emptyRecipient": "<PERSON><PERSON><PERSON> penerima harus memiliki informasi kontak"}}, "geofence": {"title": "Manajemen Geofence", "description": "Kelola area dan batas geofence Anda", "addButton": "Tambah Geofence", "typeLabel": "Tipe", "search": {"placeholder": "Cari geofence...", "sortOptions": {"createdAt": "Tanggal Dibuat", "name": "<PERSON><PERSON>", "type": "Tipe"}}, "type": {"placeholder": "<PERSON><PERSON><PERSON> tipe", "polygon": "Poligon", "circle": "Lingkaran", "rectangle": "<PERSON><PERSON><PERSON>"}, "list": {"empty": "Tidak ada geofence ditemukan", "noZone": "Tidak ada Zona", "noActiveTime": "Tidak ada waktu aktif", "noStayDuration": "Tidak ada durasi tinggal", "activeTime": "Waktu Aktif", "stayDuration": "<PERSON><PERSON><PERSON>"}, "modal": {"add": {"title": "Tambah Geofence", "success": "Geofence berhasil dibuat"}, "edit": {"title": "Edit Geofence", "success": "Geofence ber<PERSON><PERSON>er<PERSON>"}, "error": "Gagal menyimpan geofence", "sections": {"basic": "Informasi <PERSON>"}, "tabs": {"basic": "Informasi <PERSON>", "map": "<PERSON><PERSON>"}, "form": {"name": {"label": "<PERSON><PERSON>", "placeholder": "Ma<PERSON>kkan nama geofence"}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON>"}, "zone": {"label": "Zona"}, "activeTime": {"start": "<PERSON><PERSON><PERSON>", "end": "<PERSON><PERSON><PERSON>"}, "minStayDuration": "<PERSON><PERSON><PERSON> Mini<PERSON>", "maxStayDuration": "<PERSON><PERSON><PERSON>"}, "map": {"clearButton": "Hapus Titik"}}}, "beacon": {"title": "Manajemen Beacon", "description": "<PERSON><PERSON><PERSON> per<PERSON> dan konfigurasi Anda", "addButton": "Tambah Beacon", "search": {"placeholder": "Cari beacon...", "sortOptions": {"createdAt": "Tanggal Dibuat", "name": "<PERSON><PERSON>"}}, "table": {"name": "<PERSON><PERSON>", "uuid": "UUID", "major": "Major", "minor": "Minor", "status": "Status", "lastSeen": "<PERSON>rak<PERSON>"}, "noBeacons": "Tidak ada beacon ditemukan"}, "site": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> lokasi situs dan konfigu<PERSON>i <PERSON>a", "addButton": "Tambah Lokasi", "search": {"placeholder": "Cari lokasi...", "sortOptions": {"createdAt": "Tanggal Dibuat", "name": "<PERSON><PERSON>"}}, "filter": {"status": "Filter berdasarkan status"}, "status": {"active": "Aktif", "inactive": "Tidak Aktif"}, "sort": {"createdAt": "Tanggal Dibuat", "name": "<PERSON><PERSON>"}, "table": {"name": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "city": "Kota", "type": "Tipe", "checkpoints": "<PERSON><PERSON>", "status": "Status", "lastUpdated": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>"}, "list": {"empty": "Tidak ada lokasi di<PERSON>ukan", "total": "Total {{total}} item"}, "modal": {"createTitle": "Tambah Lokasi Baru", "editTitle": "<PERSON>", "createSuccess": "Lokasi berhasil dibuat", "editSuccess": "<PERSON><PERSON> <PERSON><PERSON>", "add": {"title": "Tambah Lokasi Baru", "success": "Lokasi berhasil dibuat", "error": "Gagal membuat lokasi"}, "edit": {"title": "<PERSON>", "success": "<PERSON><PERSON> <PERSON><PERSON>", "error": "<PERSON><PERSON> lo<PERSON>i"}}, "form": {"basicInfo": "Informasi <PERSON>", "locationInfo": "Informasi Lokasi", "notificationInfo": "Pengatura<PERSON>", "labels": "Label", "name": "<PERSON><PERSON>", "nameTooltip": "<PERSON><PERSON>kkan nama unik untuk lokasi ini", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama lokasi", "description": "<PERSON><PERSON><PERSON><PERSON>", "descriptionTooltip": "Berikan detail tambahan tentang lokasi ini", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> lokasi", "address": "<PERSON><PERSON><PERSON>", "addressTooltip": "<PERSON><PERSON><PERSON><PERSON> alamat fi<PERSON> lokasi", "addressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> alamat lo<PERSON>i", "timezone": "Zona Waktu", "timezoneTooltip": "Pilih zona waktu untuk lokasi ini", "coordinates": "Ko<PERSON>inat", "latitude": "<PERSON><PERSON>", "latitudePlaceholder": "<PERSON><PERSON><PERSON><PERSON> garis lintang", "longitude": "<PERSON><PERSON>", "longitudePlaceholder": "<PERSON><PERSON><PERSON><PERSON> garis bujur", "scheduleSettings": "<PERSON><PERSON><PERSON><PERSON>", "intervalActive": "Atur rentang waktu aktif", "startTime": "<PERSON><PERSON><PERSON>", "endTime": "<PERSON><PERSON><PERSON>", "emailSettings": "Pengaturan Notifika<PERSON>", "emailSubject": "<PERSON>jek <PERSON>", "emailSubjectPlaceholder": "Masukkan subjek email", "recipients": "<PERSON><PERSON><PERSON>", "addRecipient": "Tambah Penerima", "emailAddress": "<PERSON><PERSON><PERSON>", "emailPlaceholder": "<PERSON><PERSON><PERSON><PERSON> email", "nameField": "<PERSON><PERSON>", "timezoneField": "Zona Waktu", "latitudeField": "<PERSON><PERSON>", "longitudeField": "<PERSON><PERSON>", "addressField": "<PERSON><PERSON><PERSON>", "labelsField": "Label", "endTimeField": "<PERSON><PERSON><PERSON>", "emailSubjectField": "<PERSON>jek <PERSON>", "recipientsField": "<PERSON><PERSON><PERSON>", "emailField": "Email", "phoneField": "Telepon", "selectLabels": "Pilih Label"}, "sections": {"basic": "Informasi <PERSON>", "location": "<PERSON><PERSON>", "additional": "Informasi <PERSON>"}}, "checkpoint": {"title": "Manajemen Pos Pemeriksaan", "description": "<PERSON><PERSON><PERSON> pos pemeriksaan dan konfigurasinya", "add": "Tambah Pos Pemeriksaan", "search": {"placeholder": "Cari pos pemeriksaan..."}, "sort": {"createdAt": "Tanggal Dibuat", "name": "<PERSON><PERSON>"}, "list": {"empty": "Tidak ada pos pemeriksaan ditemukan", "total": "Total {{total}} item", "status": {"active": "Aktif", "inactive": "Tidak Aktif"}, "details": "Detail", "schedule": "<PERSON><PERSON><PERSON>"}, "modal": {"add": {"title": "Tambah Pos Pemeriksaan Baru", "success": "<PERSON>s pemeri<PERSON> ber<PERSON>il dibuat", "error": "<PERSON><PERSON> membuat pos pemeriksaan"}, "edit": {"title": "<PERSON> <PERSON><PERSON>", "success": "<PERSON><PERSON> pem<PERSON> be<PERSON>", "error": "<PERSON><PERSON> me<PERSON> pos pemeriks<PERSON>"}, "createTitle": "Tambah Pos Pemeriksaan Baru", "editTitle": "<PERSON> <PERSON><PERSON>", "createSuccess": "<PERSON>s pemeri<PERSON> ber<PERSON>il dibuat", "editSuccess": "<PERSON><PERSON> pem<PERSON> be<PERSON>", "form": {"name": {"label": "<PERSON>a Pos Pemeriksaan", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama pos pemeriksaan", "required": "<PERSON>a pos pemeri<PERSON><PERSON>an"}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> pos pemeriks<PERSON>"}, "type": {"label": "Tipe Pos Pemeriksaan", "placeholder": "<PERSON><PERSON>h tipe pos pemeriksaan", "required": "Tipe pos pemeri<PERSON><PERSON>an"}, "zone": {"label": "Zona", "placeholder": "<PERSON>lih zona"}, "geofence": {"label": "Geofence", "placeholder": "<PERSON>lih geofence"}, "beacon": {"label": "Beacon", "placeholder": "Pilih beacon"}, "label": {"label": "Label", "placeholder": "Pilih label"}, "coordinates": {"label": "Ko<PERSON>inat", "latitude": "<PERSON><PERSON>", "longitude": "<PERSON><PERSON>"}, "address": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON>"}, "schedule": {"title": "<PERSON><PERSON><PERSON><PERSON>", "days": {"monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON><PERSON>", "wednesday": "<PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "<PERSON><PERSON>", "saturday": "Sabtu", "sunday": "<PERSON><PERSON>"}, "visits": "kunjungan diperlukan", "enabled": "Diaktifkan", "count": "<PERSON><PERSON><PERSON>"}, "alert": {"title": "Pengaturan <PERSON>", "enabled": "Aktifkan Peringatan"}, "active": "Aktif", "basicInfo": "Informasi <PERSON>", "locationInfo": "Informasi Lokasi", "scheduleInfo": "Informasi Jadwal", "alertSettings": "Pengaturan <PERSON>", "nameTooltip": "<PERSON><PERSON><PERSON>n nama unik untuk pos pemeriksaan ini", "descriptionTooltip": "Berikan detail tambahan tentang pos pemeriksaan ini", "typeTooltip": "<PERSON><PERSON>h tipe pos pemeriksaan yang akan dibuat", "zoneTooltip": "Pilih zona untuk pos pemeriksaan ini", "geofenceTooltip": "Pilih geofence untuk mengaitkan dengan pos pemeriksaan ini", "beaconTooltip": "Pilih beacon untuk mengaitkan dengan pos pemeriksaan ini", "labelTooltip": "Pilih label untuk pos pemeriksaan ini", "coordinatesTooltip": "Masukkan koordinat GPS untuk pos pemeriksaan ini", "addressTooltip": "<PERSON><PERSON><PERSON><PERSON> alamat fisik pos pemeriksaan"}}, "types": {"qr": "Kode QR", "nfc": "Tag NFC", "beacon": "Beacon", "geofence": "Geofence"}, "error": {"noBranchCode": "Kode cabang diperlukan", "invalidCoordinates": "<PERSON>ordinat tidak valid", "createFailed": "<PERSON><PERSON> membuat pos pemeriksaan", "updateFailed": "<PERSON><PERSON> me<PERSON> pos pemeriks<PERSON>", "deleteFailed": "<PERSON><PERSON> pos pemeriks<PERSON>"}, "technical": {"details": "Detail <PERSON>", "type": "Tipe", "uuid": "UUID", "major": "Major", "minor": "Minor", "name": "<PERSON><PERSON>"}, "map": {"title": "<PERSON><PERSON>", "tooltip": "Klik pada peta untuk mengatur lokasi pos pemeriksaan", "searchPlaceholder": "Cari lokasi...", "markerTooltip": "<PERSON><PERSON>"}, "location": {"title": "Informasi Lokasi", "description": "Atur lokasi pos pemeriksaan"}}, "license": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> lisensi dan batasan-batasa<PERSON>a", "add": "Tambah Lisensi", "list": {"empty": "Tidak ada lisensi ditemukan", "error": "<PERSON>l mengam<PERSON> lisensi", "total": "Total {{total}} item"}, "table": {"name": "<PERSON><PERSON>", "maxSubbranch": "Maks Subcabang", "maxUser": "Maks <PERSON>", "maxLabel": "Maks Label", "status": "Status"}, "status": {"active": "Aktif", "inactive": "Tidak Aktif"}, "limitations": "Batasan", "additionalLimits": "<PERSON><PERSON><PERSON>", "maxSubbranch": "Maks Subcabang", "maxUser": "Maks <PERSON>", "maxLabel": "Maks Label", "maxBeacon": "Maks Beacon", "maxScheduler": "<PERSON><PERSON>", "maxAlert": "Maks <PERSON>", "maxDevice": "<PERSON><PERSON>", "maxSite": "<PERSON><PERSON>"}, "formPicklist": {"title": "<PERSON><PERSON><PERSON>", "description": "Kelola opsi daftar pilihan form Anda", "addButton": "Tambah Daftar <PERSON>", "search": {"placeholder": "<PERSON>i daftar pilihan...", "sortOptions": {"createdAt": "Tanggal Dibuat", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "sortDirection": {"asc": "<PERSON><PERSON>", "desc": "<PERSON><PERSON>"}}, "noPicklistsFound": "Tidak ada daftar pilihan di<PERSON>ukan", "list": {"status": {"active": "Aktif", "inactive": "Tidak Aktif"}, "showMore": "<PERSON><PERSON><PERSON><PERSON> lebih banyak", "showLess": "<PERSON><PERSON><PERSON><PERSON> lebih sedikit", "optionsCount": "{{count}} item", "creationInfo": "Informasi Pembuatan", "created": "Dibuat", "updated": "<PERSON><PERSON><PERSON><PERSON>", "timeFormat": "DD MMM YYYY HH:mm", "tooltips": {"edit": "<PERSON> daftar pilihan", "createdAt": "Dibuat pada {{time}}", "updatedAt": "<PERSON><PERSON><PERSON><PERSON> pada {{time}}"}}, "modal": {"createTitle": "<PERSON><PERSON><PERSON>", "updateTitle": "<PERSON> <PERSON><PERSON><PERSON>", "createSuccess": "Daftar pilihan berhasil dibuat", "updateSuccess": "<PERSON><PERSON><PERSON> pilihan ber<PERSON>", "createError": "<PERSON><PERSON> membuat daftar pilihan", "updateError": "<PERSON><PERSON> me<PERSON> daftar pilihan"}, "sections": {"basic": "Informasi <PERSON>", "basic.tooltip": "Informasi dasar tentang daftar pilihan", "options": "Opsi Daftar <PERSON>", "options.tooltip": "Tambah dan kelola opsi untuk daftar pilihan ini"}, "form": {"name": {"label": "<PERSON><PERSON>", "placeholder": "mis., Opsi Status", "tooltip": "<PERSON><PERSON><PERSON><PERSON> nama <PERSON>riptif untuk daftar pilihan Anda"}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON>...", "tooltip": "Deskripsi opsional untuk daftar pilihan ini"}, "active": {"label": "Aktif", "tooltip": "Toggle untuk mengaktifkan/menonaktifkan daftar pilihan ini"}}, "options": {"add": "Tambah Opsi", "option": "Opsi", "name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama opsi"}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> deskripsi opsi"}, "active": {"label": "Aktif"}, "dragTooltip": "<PERSON><PERSON><PERSON>"}, "validation": {"nameRequired": "<PERSON><PERSON> daftar pilihan dip<PERSON>an", "optionsRequired": "Minimal satu opsi diperlukan", "optionNameRequired": "<PERSON><PERSON><PERSON> opsi harus memiliki nama"}, "message": {"createSuccess": "Daftar pilihan berhasil dibuat", "updateSuccess": "<PERSON><PERSON><PERSON> pilihan ber<PERSON>", "deleteSuccess": "<PERSON><PERSON><PERSON> pilihan ber<PERSON>", "createError": "<PERSON><PERSON> membuat daftar pilihan", "updateError": "<PERSON><PERSON> me<PERSON> daftar pilihan", "deleteError": "<PERSON><PERSON><PERSON> daftar pilihan"}}, "forms": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> formulir dan templat <PERSON>", "addButton": "Tam<PERSON> Formulir", "search": {"placeholder": "Cari formulir...", "sortOptions": {"createdAt": "Tanggal Dibuat", "name": "<PERSON><PERSON>", "updatedAt": "<PERSON><PERSON>"}}, "noFormsFound": "Tidak ada formulir di<PERSON>n", "checkpoint": "<PERSON><PERSON>", "noCheckpoint": "Tidak ada pos pemeriksaan", "role": "<PERSON><PERSON><PERSON>", "noRole": "Tidak ada peran ditetapkan", "fields": "Bidang", "fieldCount": "bidang", "modal": {"add": {"title": "Tambah Formulir Baru"}, "edit": {"title": "<PERSON>"}, "field": {"add": "Tambah Bidang", "title": "Bidang"}, "sections": {"basic": "Informasi <PERSON>", "basic.tooltip": "Isi detail dasar formulir <PERSON>a"}, "form": {"name": {"label": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON><PERSON> nama deskriptif untuk formulir Anda", "placeholder": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>"}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Berikan detail tentang tujuan formulir", "placeholder": "<PERSON><PERSON><PERSON> tujuan dan penggunaan formulir ini..."}, "role": {"label": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON>h peran mana yang dapat mengakses formulir ini"}, "checkpoint": {"label": "Pos Pemeriksaan Terkait", "tooltip": "Hubungkan formulir ini ke pos pemeriksaan tertentu"}, "branches": {"label": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON>h cabang mana yang dapat menggunakan formulir ini"}}}}, "device": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> per<PERSON> dan konfigu<PERSON>i <PERSON>", "addButton": "Tambah Perangkat", "search": {"placeholder": "<PERSON>i perang<PERSON>...", "sortOptions": {"createdAt": "Tanggal Dibuat", "name": "<PERSON><PERSON>", "deviceName": "<PERSON><PERSON>"}}, "sort": {"createdAt": "Tanggal Dibuat", "name": "<PERSON><PERSON>"}, "imei": "IMEI", "serialNumber": "<PERSON><PERSON>", "information": "Informasi Perangkat", "noDevicesFound": "Tidak ada perangkat di<PERSON>ukan", "form": {"name": "<PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama perangkat", "type": "<PERSON><PERSON><PERSON>", "selectType": "<PERSON><PERSON><PERSON> tipe", "imei": "IMEI", "imeiPlaceholder": "Masukkan IMEI", "serialNumber": "<PERSON><PERSON>", "serialNumberPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nomor seri", "description": "<PERSON><PERSON><PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "labels": "Label", "app": "Aplikasi", "iButton": "I-<PERSON><PERSON>", "rfid": "RFID", "gps": "GPS", "requiredFields": "Harap isi semua field yang wajib", "branchRequired": "Kode cabang diperlukan", "createSuccess": "Perangkat berhasil dibuat", "updateSuccess": "<PERSON><PERSON><PERSON> ber<PERSON><PERSON>", "submitFailed": "<PERSON><PERSON>yi<PERSON> per<PERSON>"}, "modal": {"createTitle": "Tambah Perangkat Baru", "updateTitle": "<PERSON>"}, "status": {"active": "Aktif", "inactive": "Tidak Aktif"}, "list": {"empty": "Tidak ada perangkat di<PERSON>ukan", "total": "Total {{total}} item"}}, "task": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> tug<PERSON> dan pen<PERSON><PERSON>", "addButton": "Tambah Tugas", "search": {"placeholder": "Cari tugas...", "sortOptions": {"createdAt": "Tanggal Dibuat", "startTime": "<PERSON><PERSON><PERSON>", "dueDate": "Tanggal Jatuh Tempo", "priority": "Prioritas"}, "sortDirection": {"asc": "<PERSON><PERSON>", "desc": "<PERSON><PERSON>"}}, "status": {"pending": "Tertunda", "inProgress": "<PERSON><PERSON>", "completed": "Se<PERSON><PERSON>", "overdue": "Terlambat", "cancelled": "Di<PERSON><PERSON><PERSON>", "inactive": "Tidak Aktif"}, "priority": {"low": "Rendah", "medium": "Sedang", "high": "Tingg<PERSON>", "urgent": "Mendesak"}, "modal": {"add": {"title": "Tambah Tugas Baru", "success": "Tugas berhasil dibuat", "error": "<PERSON><PERSON> membuat tugas"}, "edit": {"title": "<PERSON>", "success": "<PERSON><PERSON> ber<PERSON>", "error": "<PERSON><PERSON> me<PERSON> tugas"}, "error": {"branchCode": "Kode cabang diperlukan"}, "form": {"name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama tugas", "required": "<PERSON><PERSON>an"}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tugas"}, "assignee": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> pen<PERSON> tugas"}, "startDate": {"label": "<PERSON><PERSON>"}, "dueDate": {"label": "Tanggal Jatuh Tempo"}, "priority": {"label": "Prioritas", "placeholder": "<PERSON><PERSON><PERSON> prioritas"}, "status": {"label": "Status", "placeholder": "Pilih status"}, "site": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> lokasi"}, "checkpoint": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> pos pemeriksaan"}, "dateTimeRange": {"label": "Rentang <PERSON> Waktu", "startPlaceholder": "<PERSON><PERSON>", "endPlaceholder": "<PERSON><PERSON><PERSON>"}, "fields": {"field": "Bidang", "add": "Tambah Bidang"}, "type": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> tipe tugas"}}, "sections": {"basic": "Informasi <PERSON>", "fields": "Bidang", "schedule": "<PERSON><PERSON><PERSON>"}, "create": {"success": "Tugas berhasil dibuat"}}, "list": {"empty": "Tidak ada tugas ditemukan", "assignedTo": "Ditugaskan kepada", "unassigned": "Tidak ditugaskan", "dueDate": "<PERSON><PERSON> jatuh tempo", "noDueDate": "Tidak ada tanggal jatuh tempo", "minutes": "menit", "timeRange": "Rentang <PERSON>", "role": "<PERSON><PERSON>", "fields": "Bidang", "actions": {"edit": "Edit", "delete": "Hapus", "view": "<PERSON><PERSON>"}, "checkpoint": "<PERSON><PERSON>", "createdAt": "Dibuat", "noDescription": "Tidak ada <PERSON>"}, "type": {"scheduled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "repeating": "Berulang"}}, "common": {"actions": "<PERSON><PERSON><PERSON>", "edit": "Edit", "pagination": {"total": "Total {{total}} item"}, "cancel": "<PERSON><PERSON>", "create": "Buat", "update": "<PERSON><PERSON><PERSON>", "remove": "Hapus", "created": "Dibuat pada", "createdAt": "Dibuat pada", "updatedAt": "<PERSON><PERSON><PERSON><PERSON> pada", "labels": "Label", "total": "Total", "items": "item", "save": "Simpan", "error": "Error", "showMore": "<PERSON><PERSON><PERSON><PERSON> lebih banyak", "showLess": "<PERSON><PERSON><PERSON><PERSON> lebih sedikit", "days": {"in_number": {"0": "<PERSON><PERSON>", "1": "<PERSON><PERSON>", "2": "<PERSON><PERSON><PERSON>", "3": "<PERSON><PERSON>", "4": "<PERSON><PERSON>", "5": "<PERSON><PERSON>", "6": "Sabtu"}}, "reportType": {"options": {"1": "Log Aktivitas", "2": "Alarm", "3": "<PERSON><PERSON><PERSON>a", "4": "Detail Cabang", "5": "Aktivitas Pos Pemeriksaan", "6": "Baterai Pos Pemeriksaan", "7": "Pengecualian", "8": "Pengecualian Te<PERSON><PERSON><PERSON>i", "9": "<PERSON><PERSON><PERSON>", "10": "Geofence", "11": "Peta Panas GPS", "12": "Zona Terlewatkan", "13": "<PERSON><PERSON><PERSON>", "14": "Tugas", "15": "<PERSON><PERSON><PERSON> dan <PERSON>", "16": "Waktu di Zona", "17": "Tidak Diketahui"}}, "reportFrequency": {"options": {"1": "<PERSON><PERSON>", "2": "Mingguan", "3": "Bulanan", "4": "<PERSON><PERSON><PERSON>", "5": "<PERSON><PERSON>"}}, "name": "<PERSON><PERSON>", "email": "Email", "phone": "Telepon", "role": "<PERSON><PERSON>", "timezone": "Zona Waktu", "latitude": "<PERSON><PERSON>", "longitude": "<PERSON><PERSON>", "notAvailable": "Tidak Tersedia", "viewImage": "Lihat G<PERSON>bar", "filters": "Filter"}, "analytics": {"drawer": {"title": {"activityLog": "Filter Log Aktivitas", "alarms": "Filter <PERSON>", "averageRotation": "<PERSON><PERSON>a", "branchDetails": "Filter Detail <PERSON>", "checkpointActivity": "Filter Aktivitas Pos Pemeriksaan", "checkpointBattery": "<PERSON><PERSON>i Pos Pemeriksaan", "exception": "<PERSON><PERSON>", "exceptionDetailed": "<PERSON><PERSON>", "forms": "<PERSON><PERSON>", "geofence": "<PERSON>lter G<PERSON>ce", "gpsHeatmap": "Fi<PERSON> Peta Panas GPS", "missedZone": "<PERSON><PERSON>", "signOnOff": "<PERSON><PERSON>/<PERSON><PERSON><PERSON>", "tasks": "<PERSON><PERSON>", "timeAndRotation": "<PERSON><PERSON> dan <PERSON>", "timeOnZone": "<PERSON><PERSON> Zona", "default": "<PERSON><PERSON>"}}, "form": {"dateRange": {"title": "<PERSON><PERSON><PERSON>"}, "startDate": {"label": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> tanggal mulai untuk filter"}, "endDate": {"label": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> tanggal selesai untuk filter"}, "timeRange": {"title": "Rentang <PERSON>"}, "startTime": {"label": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> waktu mulai untuk filter"}, "endTime": {"label": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON>h waktu selesai untuk filter"}, "branch": {"label": "Cabang", "tooltip": "Pilih cabang untuk filter"}, "role": {"label": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> peran untuk filter"}, "user": {"label": "Pengguna", "tooltip": "<PERSON><PERSON>h pen<PERSON>una untuk filter"}, "userLabel": {"label": "Label Pengguna", "tooltip": "Pilih label pengguna untuk filter"}, "device": {"label": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> per<PERSON>kat untuk filter"}, "deviceLabel": {"label": "Label Perangkat", "tooltip": "Pilih label perangkat untuk filter"}, "activity": {"label": "Aktivitas", "tooltip": "Pilih aktivitas untuk filter"}, "site": {"label": "<PERSON><PERSON>", "tooltip": "<PERSON>lih lokasi untuk filter"}, "siteLabel": {"label": "Label Lokasi", "tooltip": "Pilih label lokasi untuk filter"}, "checkpoint": {"label": "<PERSON><PERSON>", "tooltip": "Pilih pos pemeriksaan untuk filter"}, "checkpointLabel": {"label": "Label Pos Pemeriksaan", "tooltip": "Pilih label pos pemeriksaan untuk filter"}, "forms": {"label": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> formulir untuk filter"}, "tasks": {"label": "Tugas", "tooltip": "<PERSON><PERSON><PERSON> tugas untuk filter"}, "rotationInterval": {"label": "<PERSON><PERSON> (Menit)", "tooltip": "Masukkan interval rotasi dalam menit"}, "rotationMethod": {"label": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> metode rotasi"}, "apply": "Terap<PERSON> Filter"}, "missedZoneLog": {"expectedVisits": "Diharapkan", "actualVisits": "Aktual", "missedVisits": "Terlewat", "completionRate": "Penyelesaian"}}, "activity-log": {"title": "Log Aktivitas", "description": "Lihat dan kelola log aktivitas", "noLogs": "Tidak ada log aktivitas yang ditemukan", "photoAlt": "Foto aktivitas", "viewPhoto": "<PERSON><PERSON>", "filters": {"title": "Filter Log Aktivitas", "button": "Filter"}}, "checkpointActivityLog": {"title": "Log Aktivitas Pos Pemeriksaan", "description": "Lihat dan kelola log aktivitas pos pemeriksaan", "filters": {"title": "Filter Log Aktivitas Pos Pemeriksaan", "button": "Filter"}, "noLogs": "Tidak ada log aktivitas pos pemeriksaan ditemukan", "photoAlt": "Foto aktivitas pos pemeriksaan", "viewPhoto": "<PERSON>hat foto", "checkpointName": "<PERSON><PERSON>", "zoneName": "Zona", "location": "<PERSON><PERSON>", "dateTime": "Tanggal/Waktu", "comment": "Komentar", "id": "ID", "eventTime": "<PERSON><PERSON><PERSON>"}, "form-log": {"title": "<PERSON><PERSON>", "description": "<PERSON>hat dan kelola log formulir", "noData": "Tidak ada log formulir yang di<PERSON>ukan", "noBranch": "Tidak Ada Cabang", "branch": "Cabang", "location": "<PERSON><PERSON>", "dateTime": "Tanggal/Waktu", "viewFields": "<PERSON><PERSON>", "fields": "<PERSON><PERSON><PERSON>", "viewImage": "Lihat G<PERSON>bar", "id": "ID", "eventTime": "<PERSON><PERSON><PERSON>", "submittedTime": "<PERSON><PERSON><PERSON>", "imagePreview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filters": {"title": "<PERSON><PERSON>"}}, "geofenceLog": {"title": "Log Geofence", "description": "Lihat dan kelola log geofence", "noLogs": "Tidak ada log geofence yang ditemukan", "logTitle": "Log Geofence", "device": "<PERSON><PERSON><PERSON>", "location": "<PERSON><PERSON>", "zone": "Zona", "geofence": "Geofence", "dateTime": "Tanggal/Waktu", "activeTime": "Waktu Aktif", "id": "ID", "timezone": "Zona Waktu", "gpsTracking": "Pelacakan GPS", "gpsInterval": "Interval GPS", "filters": {"title": "Filter Log Geofence", "button": "Filter"}}}