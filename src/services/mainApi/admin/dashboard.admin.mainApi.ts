import baseMainApi from "../base.mainApi";
import {
  AdminDashboardResponse,
  AdminDashboardParams,
} from "./types/dashboard.admin.mainApi.types";

export const dashboardAdminApi = {
  getDashboardData: async (
    branchCode: string,
    params?: AdminDashboardParams
  ) => {
    const response = await baseMainApi.get<AdminDashboardResponse>(
      `/web-api/admin/${branchCode}/dashboard`,
      { params }
    );
    return response.data;
  },
};

export default dashboardAdminApi; 