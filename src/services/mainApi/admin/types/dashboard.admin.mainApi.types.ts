import { BaseResponse } from "../../types/base.mainApi.types";

// Active Users
export interface AdminDashboardActiveUser {
  token_id: number;
  user_id: string;
  user_name: string;
  device_name: string;
  device_uid: string;
  user_agent: string;
  last_used_at: string;
  expires_at: string;
  created_at: string;
}

// Sign In/Out
export interface AdminDashboardSignInOut {
  uuid: string;
  user_log_type: "SIGNIN" | "SIGNOUT" | "FORCE_SIGNOUT";
  user_name: string;
  device_name: string | null;
  timezone_id: string;
  timezone_name: string;
  latitude: number | null;
  longitude: number | null;
  event_time: string;
}

// Activity
export interface AdminDashboardActivity {
  uuid: string;
  activity_name: string;
  user_name: string;
  device_name: string;
  timezone_id: string;
  timezone_name: string;
  latitude: number | null;
  longitude: number | null;
  original_submitted_time: string;
}

// Alarm
export interface AdminDashboardAlarm {
  uuid: string;
  alarm_type: string | null;
  alarm_message: string | null;
  user_name: string;
  device_name: string;
  timezone_id: string;
  timezone_name: string;
  latitude: number | null;
  longitude: number | null;
  original_submitted_time: string;
}

// Checkpoint
export interface AdminDashboardCheckpoint {
  uuid: string;
  checkpoint_name: string;
  zone_name: string;
  user_name: string;
  device_name: string;
  timezone_id: string;
  timezone_name: string;
  latitude: number | null;
  longitude: number | null;
  original_submitted_time: string;
}

// Form
export interface AdminDashboardForm {
  uuid: string;
  form_name: string;
  user_name: string;
  device_name: string;
  timezone_id: string;
  timezone_name: string;
  latitude: number | null;
  longitude: number | null;
  original_submitted_time: string;
}

// Task
export interface AdminDashboardTask {
  uuid: string;
  task_name: string;
  user_name: string;
  device_name: string;
  timezone_id: string | null;
  timezone_name: string | null;
  latitude: number | null;
  longitude: number | null;
  original_submitted_time: string;
}

// Geofence
export interface AdminDashboardGeofence {
  uuid: string;
  geofence_name: string;
  zone_name: string;
  user_name: string;
  device_name: string;
  timezone_id: string;
  timezone_name: string;
  latitude: number | null;
  longitude: number | null;
  original_submitted_time: string;
}

// Dashboard Data
export interface AdminDashboardData {
  active_users: AdminDashboardActiveUser[];
  sign_in_out: AdminDashboardSignInOut[];
  activity: AdminDashboardActivity[];
  alarm: AdminDashboardAlarm[];
  checkpoint: AdminDashboardCheckpoint[];
  form: AdminDashboardForm[];
  task: AdminDashboardTask[];
  geofence: AdminDashboardGeofence[];
}

// Meta
export interface AdminDashboardMeta {
  timestamp: string;
  local_time: string;
  branch_timezone_id: string;
  branch_timezone_name: string;
  branch_timezone_gmt_offset: string;
}

// Dashboard Response
export interface AdminDashboardResponse {
  success: boolean;
  message: string;
  data: AdminDashboardData;
  meta: AdminDashboardMeta;
  error: any;
}

// Dashboard Params
export interface AdminDashboardParams {
  start_date?: string;
  end_date?: string;
  start_time?: string;
  end_time?: string;
  role_id?: string | number;
  user_id?: string | number;
  signonoff?: boolean;
  activity?: boolean;
  alarm?: boolean;
  checkpoint?: boolean;
  form?: boolean;
  task?: boolean;
  geofence?: boolean;
} 